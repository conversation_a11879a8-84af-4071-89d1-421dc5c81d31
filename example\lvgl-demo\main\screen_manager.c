/**
 * @file screen_manager.c
 * @brief 3屏导航系统管理器实现
 * <AUTHOR> Agent
 * @date 2025-08-02
 * 
 * 实现Screen 1(初始屏) -> Screen 2(菜单屏) -> Screen 3(QR码屏)的导航系统
 */

#include "screen_manager.h"
#include "qrcode_lvgl.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "nvs_flash.h"
#include "nvs.h"
#include "lwip/ip4_addr.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"
#include <string.h>
#include <time.h>

static const char *TAG = "SCREEN_MGR";

/*---- 静态函数前向声明 ----*/
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);

// 全局屏幕管理器实例
static screen_manager_t g_screen_manager = {0};

// 屏幕名称映射
static const char* screen_names[] = {
    "Initial Screen",
    "Menu Screen",
    "QR Code Screen",
    "Password Screen",
    "WiFi Config Screen",
    "WiFi Password Screen"
};

/*---- 核心管理函数 ----*/

bool screen_manager_init(void)
{
    ESP_LOGI(TAG, "Initializing screen manager...");
    
    // 检查是否已经初始化
    if (g_screen_manager.is_initialized) {
        ESP_LOGW(TAG, "Screen manager already initialized");
        return true;
    }
    
    // 初始化结构体
    memset(&g_screen_manager, 0, sizeof(screen_manager_t));
    g_screen_manager.current_screen = SCREEN_INITIAL;
    g_screen_manager.previous_screen = SCREEN_INITIAL;
    g_screen_manager.qr_canvas = NULL;

    // 初始化WiFi管理器
    esp_err_t wifi_ret = wifi_manager_init();
    if (wifi_ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize WiFi manager: %s", esp_err_to_name(wifi_ret));
        // 继续初始化，但WiFi功能可能不可用
    }

    // 初始化密码管理器
    memset(&g_screen_manager.password_manager, 0, sizeof(password_manager_t));
    
    // 创建初始屏幕
    g_screen_manager.screen_objects[SCREEN_INITIAL] = screen_create_initial();
    if (!g_screen_manager.screen_objects[SCREEN_INITIAL]) {
        ESP_LOGE(TAG, "Failed to create initial screen");
        return false;
    }
    
    // 加载初始屏幕
    lv_scr_load(g_screen_manager.screen_objects[SCREEN_INITIAL]);
    
    // 创建WiFi状态更新定时器
    g_screen_manager.wifi_status_timer = lv_timer_create(wifi_status_timer_callback, 5000, NULL);  // 每5秒更新一次
    if (!g_screen_manager.wifi_status_timer) {
        ESP_LOGW(TAG, "Failed to create WiFi status timer");
    }

    g_screen_manager.is_initialized = true;
    ESP_LOGI(TAG, "Screen manager initialized successfully");

    // 尝试自动连接到保存的WiFi
    esp_err_t auto_connect_ret = wifi_auto_connect_on_boot();
    if (auto_connect_ret != ESP_OK) {
        ESP_LOGW(TAG, "Auto-connect failed: %s", esp_err_to_name(auto_connect_ret));
    }

    return true;
}

screen_manager_t* screen_manager_get_instance(void)
{
    return &g_screen_manager;
}

bool screen_manager_switch_to(screen_type_t target_screen)
{
    if (!g_screen_manager.is_initialized) {
        ESP_LOGE(TAG, "Screen manager not initialized");
        return false;
    }

    if (target_screen >= SCREEN_COUNT) {
        ESP_LOGE(TAG, "Invalid target screen: %d", target_screen);
        return false;
    }

    if (target_screen == g_screen_manager.current_screen) {
        ESP_LOGW(TAG, "Already on target screen: %s", screen_names[target_screen]);
        return true;
    }

    ESP_LOGI(TAG, "Switching from %s to %s",
             screen_names[g_screen_manager.current_screen],
             screen_names[target_screen]);

    // 检查当前活动屏幕是否有效
    lv_obj_t* current_active_screen = lv_scr_act();
    if (!current_active_screen) {
        ESP_LOGE(TAG, "Current active screen is NULL");
        return false;
    }

    // 记录上一个屏幕
    g_screen_manager.previous_screen = g_screen_manager.current_screen;
    
    // 清理当前屏幕的特殊资源
    if (g_screen_manager.current_screen == SCREEN_QRCODE && g_screen_manager.qr_canvas) {
        qrcode_canvas_safe_cleanup(g_screen_manager.qr_canvas);
        g_screen_manager.qr_canvas = NULL;
    }

    // 对于QR码屏幕，每次都重新创建以确保QR码正确显示
    if (target_screen == SCREEN_QRCODE) {
        // 如果QR码屏幕已存在，先删除它
        if (g_screen_manager.screen_objects[SCREEN_QRCODE]) {
            safe_delete_screen(&g_screen_manager.screen_objects[SCREEN_QRCODE]);
        }
        // 重新创建QR码屏幕
        g_screen_manager.screen_objects[SCREEN_QRCODE] = screen_create_qrcode();
        if (!g_screen_manager.screen_objects[SCREEN_QRCODE]) {
            ESP_LOGE(TAG, "Failed to recreate QR code screen");
            return false;
        }
        ESP_LOGI(TAG, "QR code screen recreated for reliable display");
    } else {
        // 对于其他屏幕，使用按需创建策略
        if (!g_screen_manager.screen_objects[target_screen]) {
            switch (target_screen) {
                case SCREEN_INITIAL:
                    g_screen_manager.screen_objects[target_screen] = screen_create_initial();
                    break;
                case SCREEN_MENU:
                    g_screen_manager.screen_objects[target_screen] = screen_create_menu();
                    break;
                case SCREEN_PASSWORD:
                    g_screen_manager.screen_objects[target_screen] = screen_create_password();
                    break;
                case SCREEN_WIFI_CONFIG:
                    g_screen_manager.screen_objects[target_screen] = screen_create_wifi_config();
                    break;
                case SCREEN_WIFI_PASSWORD:
                    // WiFi密码屏幕需要特殊处理，因为需要传递SSID参数
                    // 这种情况应该通过专门的函数处理，而不是通过通用的switch_to
                    ESP_LOGW(TAG, "WiFi password screen should be created via dedicated function");
                    return false;
                default:
                    ESP_LOGE(TAG, "Unknown screen type: %d", target_screen);
                    return false;
            }

            if (!g_screen_manager.screen_objects[target_screen]) {
                ESP_LOGE(TAG, "Failed to create screen: %s", screen_names[target_screen]);
                return false;
            }
        }
    }

    // 验证目标屏幕对象的有效性
    if (!g_screen_manager.screen_objects[target_screen]) {
        ESP_LOGE(TAG, "Target screen object is NULL: %s", screen_names[target_screen]);
        return false;
    }

    // 验证目标屏幕对象是否为有效的LVGL对象
    if (!lv_obj_is_valid(g_screen_manager.screen_objects[target_screen])) {
        ESP_LOGE(TAG, "Target screen object is invalid: %s", screen_names[target_screen]);
        // 清理无效的屏幕对象引用
        g_screen_manager.screen_objects[target_screen] = NULL;
        return false;
    }

    // 安全地切换屏幕
    ESP_LOGI(TAG, "Loading target screen: %s", screen_names[target_screen]);
    lv_scr_load(g_screen_manager.screen_objects[target_screen]);
    g_screen_manager.current_screen = target_screen;

    ESP_LOGI(TAG, "Successfully switched to %s", screen_names[target_screen]);

    return true;
}

screen_type_t screen_manager_get_current_screen(void)
{
    return g_screen_manager.current_screen;
}

bool screen_manager_go_back(void)
{
    return screen_manager_switch_to(g_screen_manager.previous_screen);
}

void screen_manager_cleanup(void)
{
    ESP_LOGI(TAG, "Cleaning up screen manager...");

    // 停止WiFi自动重连
    stop_wifi_auto_reconnect();

    // 清理WiFi相关定时器
    if (g_screen_manager.wifi_status_timer) {
        lv_timer_del(g_screen_manager.wifi_status_timer);
        g_screen_manager.wifi_status_timer = NULL;
    }

    if (g_screen_manager.wifi_connection_timeout_timer) {
        lv_timer_del(g_screen_manager.wifi_connection_timeout_timer);
        g_screen_manager.wifi_connection_timeout_timer = NULL;
    }

    // 清理QR码资源
    if (g_screen_manager.qr_canvas) {
        qrcode_canvas_safe_cleanup(g_screen_manager.qr_canvas);
        g_screen_manager.qr_canvas = NULL;
    }

    // 删除所有屏幕对象
    for (int i = 0; i < SCREEN_COUNT; i++) {
        safe_delete_screen(&g_screen_manager.screen_objects[i]);
    }

    // 重置状态
    memset(&g_screen_manager, 0, sizeof(screen_manager_t));

    ESP_LOGI(TAG, "Screen manager cleanup complete");
}

/*---- WiFi字符串处理辅助函数 ----*/

// 更新自定义SSID显示
static void update_custom_ssid_display(lv_obj_t* screen)
{
    if (!screen) {
        return;
    }

    // 查找SSID显示标签（在SSID输入标签之后的第一个标签）
    lv_obj_t* ssid_display = NULL;
    uint32_t child_count = lv_obj_get_child_cnt(screen);
    bool found_ssid_label = false;

    for (uint32_t i = 0; i < child_count; i++) {
        lv_obj_t* child = lv_obj_get_child(screen, i);
        if (lv_obj_check_type(child, &lv_label_class)) {
            const char* text = lv_label_get_text(child);
            if (text && strstr(text, "Network Name")) {
                found_ssid_label = true;
                continue;
            }
            if (found_ssid_label) {
                // 这应该是SSID显示标签
                ssid_display = child;
                break;
            }
        }
    }

    if (ssid_display) {
        // 创建显示文本（显示星号以保护SSID隐私，但也显示实际字符用于调试）
        char display_text[64];
        if (g_screen_manager.wifi_manager.custom_ssid_length > 0) {
            snprintf(display_text, sizeof(display_text), "%s", g_screen_manager.wifi_manager.custom_ssid);
        } else {
            strcpy(display_text, "");
        }

        lv_label_set_text(ssid_display, display_text);
        ESP_LOGD(TAG, "Updated custom SSID display: '%s'", display_text);
    } else {
        ESP_LOGW(TAG, "SSID display label not found");
    }
}

// 清理和验证SSID字符串，移除不可打印字符
static void sanitize_ssid_string(char* dest, const uint8_t* src, size_t dest_size)
{
    if (!dest || !src || dest_size == 0) {
        return;
    }

    memset(dest, 0, dest_size);

    size_t dest_idx = 0;
    for (size_t src_idx = 0; src_idx < 32 && src[src_idx] != 0 && dest_idx < dest_size - 1; src_idx++) {
        uint8_t ch = src[src_idx];

        // 接受可打印的ASCII字符（32-126）和基本的UTF-8字符
        if ((ch >= 32 && ch <= 126) || (ch >= 128 && ch <= 255)) {
            dest[dest_idx++] = ch;
        } else if (ch > 0 && ch < 32) {
            // 将控制字符替换为问号
            dest[dest_idx++] = '?';
        }
        // 跳过null字符和其他无效字符
    }

    dest[dest_idx] = '\0';  // 确保null终止
}

/*---- WiFi密码屏幕安全辅助函数 ----*/

// 安全地查找状态标签
lv_obj_t* find_status_label(lv_obj_t* screen)
{
    if (!screen) return NULL;

    // 遍历屏幕的所有子对象，查找状态标签
    for (uint32_t i = 0; i < lv_obj_get_child_cnt(screen); i++) {
        lv_obj_t* child = lv_obj_get_child(screen, i);
        if (child && lv_obj_check_type(child, &lv_label_class)) {
            // 检查标签文本是否包含状态相关内容
            const char* text = lv_label_get_text(child);
            if (text && (strstr(text, "Connecting") || strstr(text, "Connected") ||
                        strstr(text, "failed") || strstr(text, "empty") ||
                        strstr(text, "not found") || strlen(text) == 0)) {
                return child;
            }
        }
    }
    return NULL;
}

// 安全地查找连接按钮
lv_obj_t* find_connect_button(lv_obj_t* screen)
{
    if (!screen) return NULL;

    // 遍历屏幕的所有子对象，查找按钮容器
    for (uint32_t i = 0; i < lv_obj_get_child_cnt(screen); i++) {
        lv_obj_t* child = lv_obj_get_child(screen, i);
        if (child) {
            // 检查是否是按钮容器（包含按钮的容器）
            for (uint32_t j = 0; j < lv_obj_get_child_cnt(child); j++) {
                lv_obj_t* btn = lv_obj_get_child(child, j);
                if (btn && lv_obj_check_type(btn, &lv_btn_class)) {
                    // 检查按钮标签是否是"Connect"
                    lv_obj_t* label = lv_obj_get_child(btn, 0);
                    if (label && lv_obj_check_type(label, &lv_label_class)) {
                        const char* text = lv_label_get_text(label);
                        if (text && strcmp(text, "Connect") == 0) {
                            return btn;
                        }
                    }
                }
            }
        }
    }
    return NULL;
}

// 延迟清理屏幕对象的定时器回调
void delayed_screen_cleanup_timer_cb(lv_timer_t* timer)
{
    screen_type_t* screen_type_ptr = (screen_type_t*)timer->user_data;
    if (!screen_type_ptr) {
        ESP_LOGE(TAG, "Invalid screen type in cleanup timer");
        lv_timer_del(timer);
        return;
    }

    screen_type_t screen_type = *screen_type_ptr;
    free(screen_type_ptr);  // 释放分配的内存

    ESP_LOGI(TAG, "Delayed cleanup for screen: %s", screen_names[screen_type]);

    if (g_screen_manager.screen_objects[screen_type]) {
        safe_delete_screen(&g_screen_manager.screen_objects[screen_type]);
    }

    lv_timer_del(timer);
}

// WiFi连接超时定时器回调
void wifi_connection_timeout_timer_cb(lv_timer_t* timer)
{
    ESP_LOGW(TAG, "WiFi connection timeout");

    // 清理超时定时器引用
    if (g_screen_manager.wifi_connection_timeout_timer == timer) {
        g_screen_manager.wifi_connection_timeout_timer = NULL;
    }

    // 如果仍在WiFi密码输入屏幕，显示超时错误
    if (g_screen_manager.current_screen == SCREEN_WIFI_PASSWORD) {
        handle_wifi_connection_result(false, "Connection timeout");
    }

    // 断开WiFi连接
    wifi_disconnect();

    lv_timer_del(timer);
}

// WiFi自动重连定时器回调
void wifi_auto_reconnect_timer_cb(lv_timer_t* timer)
{
    ESP_LOGI(TAG, "WiFi auto-reconnect timer triggered (attempt %lu)",
             (unsigned long)(g_screen_manager.reconnect_attempt_count + 1));

    // 检查是否已经连接
    if (g_screen_manager.wifi_manager.status == WIFI_STATUS_CONNECTED) {
        ESP_LOGI(TAG, "Already connected, stopping auto-reconnect");
        stop_wifi_auto_reconnect();
        return;
    }

    // 检查是否有保存的WiFi凭据
    if (strlen(g_screen_manager.wifi_manager.saved_ssid) == 0) {
        ESP_LOGW(TAG, "No saved WiFi credentials for auto-reconnect");
        stop_wifi_auto_reconnect();
        return;
    }

    // 增加重连尝试次数
    g_screen_manager.reconnect_attempt_count++;

    // 限制最大重连次数（避免无限重连）
    const uint32_t MAX_RECONNECT_ATTEMPTS = 10;
    if (g_screen_manager.reconnect_attempt_count > MAX_RECONNECT_ATTEMPTS) {
        ESP_LOGW(TAG, "Max reconnect attempts reached, stopping auto-reconnect");
        stop_wifi_auto_reconnect();
        return;
    }

    // 尝试重连到保存的WiFi
    ESP_LOGI(TAG, "Attempting auto-reconnect to: %s", g_screen_manager.wifi_manager.saved_ssid);
    esp_err_t ret = wifi_connect(g_screen_manager.wifi_manager.saved_ssid,
                                g_screen_manager.wifi_manager.saved_password);

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Auto-reconnect failed to initiate: %s", esp_err_to_name(ret));
    }

    // 使用指数退避算法调整重连间隔
    uint32_t next_delay = 5000 * (1 << (g_screen_manager.reconnect_attempt_count - 1));
    if (next_delay > 60000) {  // 最大60秒间隔
        next_delay = 60000;
    }

    // 更新定时器周期
    lv_timer_set_period(timer, next_delay);
    ESP_LOGI(TAG, "Next auto-reconnect attempt in %lu ms", (unsigned long)next_delay);
}

// WiFi连接成功后的定时器回调
void wifi_connection_success_timer_cb(lv_timer_t* timer)
{
    ESP_LOGI(TAG, "WiFi connection success timer callback");

    // 检查屏幕管理器是否已初始化
    if (!g_screen_manager.is_initialized) {
        ESP_LOGE(TAG, "Screen manager not initialized in timer callback");
        lv_timer_del(timer);
        return;
    }

    // 检查当前是否仍在WiFi密码屏幕
    if (g_screen_manager.current_screen != SCREEN_WIFI_PASSWORD) {
        ESP_LOGW(TAG, "Not on WiFi password screen, skipping cleanup");
        lv_timer_del(timer);
        return;
    }

    // 使用安全的屏幕切换函数
    bool switch_success = screen_manager_switch_to(SCREEN_INITIAL);
    if (!switch_success) {
        ESP_LOGE(TAG, "Failed to switch to initial screen");
        lv_timer_del(timer);
        return;
    }

    // 创建延迟清理定时器，避免立即删除屏幕对象
    screen_type_t* screen_type_ptr = malloc(sizeof(screen_type_t));
    if (screen_type_ptr) {
        *screen_type_ptr = SCREEN_WIFI_PASSWORD;
        lv_timer_t* cleanup_timer = lv_timer_create(delayed_screen_cleanup_timer_cb, 500, screen_type_ptr);
        if (cleanup_timer) {
            lv_timer_set_repeat_count(cleanup_timer, 1);
            ESP_LOGI(TAG, "Created delayed cleanup timer for WiFi password screen");
        } else {
            free(screen_type_ptr);
            ESP_LOGE(TAG, "Failed to create cleanup timer");
        }
    }

    // 清理密码输入缓冲区
    memset(g_screen_manager.wifi_manager.input_password, 0, sizeof(g_screen_manager.wifi_manager.input_password));
    g_screen_manager.wifi_manager.input_password_length = 0;

    ESP_LOGI(TAG, "WiFi connection success cleanup completed");

    // 删除定时器
    lv_timer_del(timer);
}

/*---- Screen 1: 初始屏幕实现 ----*/

lv_obj_t* screen_create_initial(void)
{
    ESP_LOGI(TAG, "Creating initial screen...");
    
    // 创建屏幕对象
    lv_obj_t* screen = lv_obj_create(NULL);
    if (!screen) {
        ESP_LOGE(TAG, "Failed to create initial screen object");
        return NULL;
    }
    
    // 设置屏幕背景
    lv_obj_set_style_bg_color(screen, lv_color_hex(0x003366), 0);  // 深蓝色背景

    // 确保屏幕不可滚动，移除滚动条
    lv_obj_clear_flag(screen, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(screen, LV_SCROLLBAR_MODE_OFF);
    
    // 创建标题标签
    lv_obj_t* title_label = lv_label_create(screen);
    lv_label_set_text(title_label, "Welcome Screen");
    lv_obj_set_style_text_color(title_label, lv_color_white(), 0);
    // 使用条件编译选择可用的字体
#if LV_FONT_MONTSERRAT_18
    lv_obj_set_style_text_font(title_label, &lv_font_montserrat_18, 0);
#elif LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(title_label, &lv_font_montserrat_14, 0);
#else
    lv_obj_set_style_text_font(title_label, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(title_label, LV_ALIGN_TOP_MID, 0, 30);

    // 标题标签不需要事件处理

    // 创建WiFi状态指示器
    lv_obj_t* wifi_indicator = create_wifi_status_indicator(screen);
    if (wifi_indicator) {
        // 保存WiFi指示器引用到全局管理器
        g_screen_manager.wifi_status_indicator = wifi_indicator;

        // 初始更新WiFi状态
        update_wifi_status_indicator(wifi_indicator, wifi_get_status(), wifi_get_signal_strength());

        ESP_LOGI(TAG, "WiFi status indicator created");
    } else {
        ESP_LOGW(TAG, "Failed to create WiFi status indicator");
    }
    
    // 创建Left按钮
    lv_obj_t* left_btn = create_centered_button(screen, "Left",
                                               -80, -20, 120, 50,
                                               screen_initial_button_handler,
                                               (void*)0);  // user_data = 0 for left

    // 创建Right按钮
    lv_obj_t* right_btn = create_centered_button(screen, "Right",
                                                80, -20, 120, 50,
                                                screen_initial_button_handler,
                                                (void*)1);  // user_data = 1 for right

    // 创建WIFI按钮（底部）
    lv_obj_t* wifi_btn = create_centered_button(screen, "WIFI",
                                               0, 80, 120, 50,
                                               screen_wifi_button_handler,
                                               NULL);  // 直接跳转到密码验证

    if (!left_btn || !right_btn || !wifi_btn) {
        ESP_LOGE(TAG, "Failed to create initial screen buttons");
        lv_obj_del(screen);
        return NULL;
    }
    
    ESP_LOGI(TAG, "Initial screen created successfully");
    return screen;
}

void screen_initial_button_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED) {
        void* user_data = lv_event_get_user_data(e);
        int button_id = (int)user_data;

        ESP_LOGI(TAG, "Initial screen button %d clicked", button_id);

        // 无论点击哪个按钮都跳转到菜单屏幕
        screen_manager_switch_to(SCREEN_MENU);
    }
}

// WIFI按钮事件处理函数
void screen_wifi_button_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED) {
        ESP_LOGI(TAG, "WIFI button clicked, switching to password screen");

        // 直接跳转到密码验证屏幕
        screen_manager_switch_to(SCREEN_PASSWORD);
    }
}

/*---- 辅助函数实现 ----*/

lv_obj_t* create_centered_button(lv_obj_t* parent, const char* text, 
                                lv_coord_t x_offset, lv_coord_t y_offset,
                                lv_coord_t width, lv_coord_t height,
                                lv_event_cb_t event_handler, void* user_data)
{
    if (!parent || !text || !event_handler) {
        ESP_LOGE(TAG, "Invalid parameters for button creation");
        return NULL;
    }
    
    // 创建按钮
    lv_obj_t* btn = lv_btn_create(parent);
    if (!btn) {
        ESP_LOGE(TAG, "Failed to create button object");
        return NULL;
    }
    
    // 设置按钮尺寸和位置
    lv_obj_set_size(btn, width, height);
    lv_obj_align(btn, LV_ALIGN_CENTER, x_offset, y_offset);
    
    // 设置按钮样式
    lv_obj_set_style_bg_color(btn, lv_color_hex(0x4CAF50), 0);  // 绿色背景
    lv_obj_set_style_bg_color(btn, lv_color_hex(0x45a049), LV_STATE_PRESSED);  // 按下时深绿色
    lv_obj_set_style_radius(btn, 8, 0);  // 圆角
    
    // 创建按钮标签
    lv_obj_t* label = lv_label_create(btn);
    lv_label_set_text(label, text);
    lv_obj_set_style_text_color(label, lv_color_white(), 0);
    lv_obj_center(label);
    
    // 添加事件处理器
    lv_obj_add_event_cb(btn, event_handler, LV_EVENT_CLICKED, user_data);
    
    return btn;
}

void safe_delete_screen(lv_obj_t** screen)
{
    if (!screen || !*screen) {
        return;
    }

    // 检查对象是否仍然有效
    if (!lv_obj_is_valid(*screen)) {
        ESP_LOGW(TAG, "Screen object is already invalid, clearing reference");
        *screen = NULL;
        return;
    }

    // 检查是否是当前活动屏幕
    lv_obj_t* current_active = lv_scr_act();
    if (*screen == current_active) {
        ESP_LOGW(TAG, "Cannot delete currently active screen");
        return;
    }

    ESP_LOGI(TAG, "Safely deleting screen object");
    lv_obj_del(*screen);
    *screen = NULL;
}

/*---- 调试函数实现 ----*/

void screen_manager_print_status(void)
{
    ESP_LOGI(TAG, "=== Screen Manager Status ===");
    ESP_LOGI(TAG, "Initialized: %s", g_screen_manager.is_initialized ? "Yes" : "No");
    ESP_LOGI(TAG, "Current Screen: %s", screen_names[g_screen_manager.current_screen]);
    ESP_LOGI(TAG, "Previous Screen: %s", screen_names[g_screen_manager.previous_screen]);
    ESP_LOGI(TAG, "QR Canvas: %s", g_screen_manager.qr_canvas ? "Active" : "NULL");
    
    for (int i = 0; i < SCREEN_COUNT; i++) {
        ESP_LOGI(TAG, "Screen %d (%s): %s", i, screen_names[i], 
                 g_screen_manager.screen_objects[i] ? "Created" : "NULL");
    }
    ESP_LOGI(TAG, "=============================");
}

const char* screen_get_type_name(screen_type_t screen_type)
{
    if (screen_type < SCREEN_COUNT) {
        return screen_names[screen_type];
    }
    return "Unknown Screen";
}

/*---- Screen 2: 菜单屏幕实现 ----*/

lv_obj_t* screen_create_menu(void)
{
    ESP_LOGI(TAG, "Creating menu screen...");

    // 创建屏幕对象
    lv_obj_t* screen = lv_obj_create(NULL);
    if (!screen) {
        ESP_LOGE(TAG, "Failed to create menu screen object");
        return NULL;
    }

    // 设置屏幕背景
    lv_obj_set_style_bg_color(screen, lv_color_hex(0x2E7D32), 0);  // 深绿色背景

    // 确保屏幕不可滚动，移除滚动条
    lv_obj_clear_flag(screen, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(screen, LV_SCROLLBAR_MODE_OFF);

    // 创建标题标签
    lv_obj_t* title_label = lv_label_create(screen);
    lv_label_set_text(title_label, "Menu Screen");
    lv_obj_set_style_text_color(title_label, lv_color_white(), 0);
    // 使用条件编译选择可用的字体
#if LV_FONT_MONTSERRAT_18
    lv_obj_set_style_text_font(title_label, &lv_font_montserrat_18, 0);
#elif LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(title_label, &lv_font_montserrat_14, 0);
#else
    lv_obj_set_style_text_font(title_label, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(title_label, LV_ALIGN_TOP_MID, 0, 20);

    // 创建2x2按钮网格
    // 按钮1 (左上)
    lv_obj_t* btn1 = create_centered_button(screen, "Button 1",
                                           -70, -40, 100, 40,
                                           screen_menu_button_handler,
                                           (void*)1);

    // 按钮2 (右上)
    lv_obj_t* btn2 = create_centered_button(screen, "Button 2",
                                           70, -40, 100, 40,
                                           screen_menu_button_handler,
                                           (void*)2);

    // 按钮3 (左下)
    lv_obj_t* btn3 = create_centered_button(screen, "Button 3",
                                           -70, 40, 100, 40,
                                           screen_menu_button_handler,
                                           (void*)3);

    // 按钮4 (右下)
    lv_obj_t* btn4 = create_centered_button(screen, "Button 4",
                                           70, 40, 100, 40,
                                           screen_menu_button_handler,
                                           (void*)4);

    if (!btn1 || !btn2 || !btn3 || !btn4) {
        ESP_LOGE(TAG, "Failed to create menu screen buttons");
        lv_obj_del(screen);
        return NULL;
    }

    ESP_LOGI(TAG, "Menu screen created successfully");
    return screen;
}

void screen_menu_button_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED) {
        void* user_data = lv_event_get_user_data(e);
        int button_id = (int)user_data;

        ESP_LOGI(TAG, "Menu screen button %d clicked", button_id);

        // 无论点击哪个按钮都跳转到QR码屏幕
        screen_manager_switch_to(SCREEN_QRCODE);
    }
}

/*---- Screen 3: QR码屏幕实现 ----*/

lv_obj_t* screen_create_qrcode(void)
{
    ESP_LOGI(TAG, "Creating QR code screen...");

    // 创建屏幕对象
    lv_obj_t* screen = lv_obj_create(NULL);
    if (!screen) {
        ESP_LOGE(TAG, "Failed to create QR code screen object");
        return NULL;
    }

    // 设置屏幕背景
    lv_obj_set_style_bg_color(screen, lv_color_hex(0x1A237E), 0);  // 深蓝紫色背景

    // 确保屏幕不可滚动，移除滚动条
    lv_obj_clear_flag(screen, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(screen, LV_SCROLLBAR_MODE_OFF);

    // 创建标题标签
    lv_obj_t* title_label = lv_label_create(screen);
    lv_label_set_text(title_label, "QR Code Screen");
    lv_obj_set_style_text_color(title_label, lv_color_white(), 0);
    // 使用条件编译选择可用的字体
#if LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(title_label, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_18
    lv_obj_set_style_text_font(title_label, &lv_font_montserrat_18, 0);
#else
    lv_obj_set_style_text_font(title_label, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(title_label, LV_ALIGN_TOP_MID, 0, 10);

    // 创建提示标签
    lv_obj_t* hint_label = lv_label_create(screen);
    lv_label_set_text(hint_label, "Touch QR code to return");
    lv_obj_set_style_text_color(hint_label, lv_color_hex(0xCCCCCC), 0);
    // 使用条件编译选择可用的字体
#if LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(hint_label, &lv_font_montserrat_12, 0);
#elif LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(hint_label, &lv_font_montserrat_14, 0);
#else
    lv_obj_set_style_text_font(hint_label, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(hint_label, LV_ALIGN_BOTTOM_MID, 0, -10);

    // 创建QR码
    g_screen_manager.qr_canvas = qrcode_show_alipay_canvas_safe(screen);
    if (!g_screen_manager.qr_canvas) {
        ESP_LOGE(TAG, "Failed to create QR code canvas");
        lv_obj_del(screen);
        return NULL;
    }

    // 为QR码添加触摸事件处理器
    lv_obj_add_event_cb(g_screen_manager.qr_canvas, screen_qrcode_touch_handler,
                        LV_EVENT_CLICKED, NULL);

    // 确保QR码可以接收点击事件
    lv_obj_add_flag(g_screen_manager.qr_canvas, LV_OBJ_FLAG_CLICKABLE);

    ESP_LOGI(TAG, "QR code screen created successfully");
    return screen;
}

void screen_qrcode_touch_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED) {
        ESP_LOGI(TAG, "QR code touched, returning to initial screen");

        // 返回到初始屏幕
        screen_manager_switch_to(SCREEN_INITIAL);
    }
}

/*---- WiFi状态指示器实现 ----*/

lv_obj_t* create_wifi_status_indicator(lv_obj_t* parent)
{
    if (!parent) {
        ESP_LOGE(TAG, "Parent object is NULL");
        return NULL;
    }

    // 创建WiFi状态文字标签
    lv_obj_t* status_label = lv_label_create(parent);
    if (!status_label) {
        ESP_LOGE(TAG, "Failed to create WiFi status label");
        return NULL;
    }

    // 设置标签样式和位置
    lv_label_set_text(status_label, "Not Connected");  // 默认显示未连接
    lv_obj_set_style_text_color(status_label, lv_color_hex(0x888888), 0);  // 灰色
    lv_obj_align(status_label, LV_ALIGN_TOP_RIGHT, -10, 10);

    // 应用字体（使用条件编译）
#if defined(CONFIG_LV9_FONT_SOURCE_HAN_SANS_SC_14_CJK) || defined(LV_FONT_SOURCE_HAN_SANS_SC_14_CJK)
    lv_obj_set_style_text_font(status_label, &lv_font_source_han_sans_sc_14_cjk, 0);
#elif LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(status_label, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(status_label, &lv_font_montserrat_12, 0);
#else
    lv_obj_set_style_text_font(status_label, LV_FONT_DEFAULT, 0);
#endif

    ESP_LOGI(TAG, "WiFi status indicator created successfully");
    return status_label;
}

void update_wifi_status_indicator(lv_obj_t* status_label, wifi_status_t status, int8_t signal_strength)
{
    if (!status_label) {
        return;
    }

    // 根据WiFi状态更新文字和颜色
    switch (status) {
        case WIFI_STATUS_CONNECTED:
            lv_label_set_text(status_label, "Connected");
            lv_obj_set_style_text_color(status_label, lv_color_hex(0x00FF00), 0);  // 绿色
            break;

        case WIFI_STATUS_CONNECTING:
            lv_label_set_text(status_label, "Connecting..");
            lv_obj_set_style_text_color(status_label, lv_color_hex(0xFFFF00), 0);  // 黄色
            break;

        case WIFI_STATUS_SCANNING:
            lv_label_set_text(status_label, "Scanning..");
            lv_obj_set_style_text_color(status_label, lv_color_hex(0x00FFFF), 0);  // 青色
            break;

        case WIFI_STATUS_FAILED:
            lv_label_set_text(status_label, "Failed");
            lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
            break;

        case WIFI_STATUS_DISCONNECTED:
        default:
            lv_label_set_text(status_label, "Not Connected");
            lv_obj_set_style_text_color(status_label, lv_color_hex(0x888888), 0);  // 灰色
            break;
    }
}

/*---- 按钮事件处理实现 ----*/

/*---- WiFi管理函数实现 ----*/

esp_err_t wifi_manager_init(void)
{
    ESP_LOGI(TAG, "Initializing WiFi manager...");

    // 初始化WiFi管理器结构体
    memset(&g_screen_manager.wifi_manager, 0, sizeof(wifi_manager_t));
    g_screen_manager.wifi_manager.status = WIFI_STATUS_DISCONNECTED;
    g_screen_manager.wifi_manager.auto_connect_enabled = true;

    // 加载保存的WiFi凭据到内存中（用于自动重连）
    char saved_ssid[33] = {0};
    char saved_password[65] = {0};
    esp_err_t load_ret = load_wifi_credentials(saved_ssid, saved_password);
    if (load_ret == ESP_OK) {
        strncpy(g_screen_manager.wifi_manager.saved_ssid, saved_ssid,
               sizeof(g_screen_manager.wifi_manager.saved_ssid) - 1);
        strncpy(g_screen_manager.wifi_manager.saved_password, saved_password,
               sizeof(g_screen_manager.wifi_manager.saved_password) - 1);
        ESP_LOGI(TAG, "Loaded saved WiFi credentials for auto-reconnect: %s", saved_ssid);
    } else {
        ESP_LOGI(TAG, "No saved WiFi credentials found");
    }

    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 初始化TCP/IP适配器
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();

    // 注册WiFi事件处理器
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL));

    // 初始化WiFi
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "WiFi manager initialized successfully");
    return ESP_OK;
}

esp_err_t wifi_start_scan(void)
{
    ESP_LOGI(TAG, "Starting WiFi scan...");

    g_screen_manager.wifi_manager.status = WIFI_STATUS_SCANNING;

    wifi_scan_config_t scan_config = {
        .ssid = NULL,
        .bssid = NULL,
        .channel = 0,
        .show_hidden = false,
        .scan_type = WIFI_SCAN_TYPE_ACTIVE,
        .scan_time.active.min = 100,
        .scan_time.active.max = 300,
    };

    return esp_wifi_scan_start(&scan_config, false);
}

wifi_status_t wifi_get_status(void)
{
    return g_screen_manager.wifi_manager.status;
}

int8_t wifi_get_signal_strength(void)
{
    return g_screen_manager.wifi_manager.signal_strength;
}

esp_err_t wifi_connect(const char* ssid, const char* password)
{
    if (!ssid) {
        ESP_LOGE(TAG, "SSID is NULL");
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "Connecting to WiFi: %s", ssid);

    wifi_config_t wifi_config = {0};
    strncpy((char*)wifi_config.sta.ssid, ssid, sizeof(wifi_config.sta.ssid) - 1);
    if (password) {
        strncpy((char*)wifi_config.sta.password, password, sizeof(wifi_config.sta.password) - 1);
    }

    g_screen_manager.wifi_manager.status = WIFI_STATUS_CONNECTING;

    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    return esp_wifi_connect();
}

esp_err_t wifi_disconnect(void)
{
    ESP_LOGI(TAG, "Disconnecting WiFi...");
    g_screen_manager.wifi_manager.status = WIFI_STATUS_DISCONNECTED;
    return esp_wifi_disconnect();
}

esp_err_t wifi_get_scan_results(wifi_scan_result_t* results)
{
    if (!results) {
        return ESP_ERR_INVALID_ARG;
    }

    uint16_t ap_count = 0;
    esp_err_t ret = esp_wifi_scan_get_ap_num(&ap_count);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get AP count: %s", esp_err_to_name(ret));
        return ret;
    }

    if (ap_count == 0) {
        results->ap_list = NULL;
        results->ap_count = 0;
        results->max_count = 0;
        results->scan_complete = true;
        return ESP_OK;
    }

    // 分配内存存储扫描结果
    wifi_ap_record_t* ap_records = malloc(ap_count * sizeof(wifi_ap_record_t));
    if (!ap_records) {
        ESP_LOGE(TAG, "Failed to allocate memory for AP records");
        return ESP_ERR_NO_MEM;
    }

    ret = esp_wifi_scan_get_ap_records(&ap_count, ap_records);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get AP records: %s", esp_err_to_name(ret));
        free(ap_records);
        return ret;
    }

    // 分配我们自己的AP信息结构体
    results->ap_list = malloc(ap_count * sizeof(wifi_ap_info_t));
    if (!results->ap_list) {
        ESP_LOGE(TAG, "Failed to allocate memory for AP info");
        free(ap_records);
        return ESP_ERR_NO_MEM;
    }

    // 转换数据格式
    for (int i = 0; i < ap_count; i++) {
        // 使用安全的SSID清理函数
        sanitize_ssid_string(results->ap_list[i].ssid, ap_records[i].ssid, sizeof(results->ap_list[i].ssid));

        results->ap_list[i].rssi = ap_records[i].rssi;
        results->ap_list[i].authmode = ap_records[i].authmode;
        results->ap_list[i].is_open = (ap_records[i].authmode == WIFI_AUTH_OPEN);
        results->ap_list[i].channel = ap_records[i].primary;

        // 调试日志：显示处理后的SSID
        ESP_LOGD(TAG, "Processed SSID[%d]: '%s' (RSSI: %d, Auth: %d)",
                 i, results->ap_list[i].ssid, results->ap_list[i].rssi, results->ap_list[i].authmode);
    }

    results->ap_count = ap_count;
    results->max_count = ap_count;
    results->scan_complete = true;

    free(ap_records);

    ESP_LOGI(TAG, "WiFi scan results retrieved: %u networks", (unsigned int)ap_count);
    return ESP_OK;
}

void wifi_free_scan_results(wifi_scan_result_t* results)
{
    if (results && results->ap_list) {
        free(results->ap_list);
        results->ap_list = NULL;
        results->ap_count = 0;
        results->max_count = 0;
        results->scan_complete = false;
    }
}

/*---- 密码验证函数实现 ----*/

bool validate_admin_password(const char* input)
{
    if (!input) {
        return false;
    }
    return strcmp(input, ADMIN_PASSWORD) == 0;
}

bool is_password_locked(void)
{
    if (!g_screen_manager.password_manager.is_locked) {
        return false;
    }

    uint32_t current_time = lv_tick_get();
    uint32_t elapsed = current_time - g_screen_manager.password_manager.lockout_start_time;

    if (elapsed >= LOCKOUT_DURATION_MS) {
        // 锁定时间已过，解除锁定
        g_screen_manager.password_manager.is_locked = false;
        g_screen_manager.password_manager.failed_attempts = 0;
        ESP_LOGI(TAG, "Password lockout expired, access restored");
        return false;
    }

    return true;
}

void reset_password_attempts(void)
{
    g_screen_manager.password_manager.failed_attempts = 0;
    g_screen_manager.password_manager.is_locked = false;
    memset(g_screen_manager.password_manager.input_buffer, 0, sizeof(g_screen_manager.password_manager.input_buffer));
    g_screen_manager.password_manager.input_length = 0;
    ESP_LOGI(TAG, "Password attempts reset");
}

void increment_failed_attempts(void)
{
    g_screen_manager.password_manager.failed_attempts++;
    ESP_LOGI(TAG, "Failed password attempt %u/%d",
             (unsigned int)g_screen_manager.password_manager.failed_attempts, MAX_PASSWORD_ATTEMPTS);

    if (g_screen_manager.password_manager.failed_attempts >= MAX_PASSWORD_ATTEMPTS) {
        g_screen_manager.password_manager.is_locked = true;
        g_screen_manager.password_manager.lockout_start_time = lv_tick_get();
        ESP_LOGW(TAG, "Password locked for %d minutes", LOCKOUT_DURATION_MS / (60 * 1000));
    }
}

uint8_t get_remaining_attempts(void)
{
    if (g_screen_manager.password_manager.failed_attempts >= MAX_PASSWORD_ATTEMPTS) {
        return 0;
    }
    return MAX_PASSWORD_ATTEMPTS - g_screen_manager.password_manager.failed_attempts;
}

/*---- NVS WiFi凭据存储实现 ----*/

#define NVS_WIFI_NAMESPACE "wifi_config"
#define NVS_WIFI_SSID_KEY "ssid"
#define NVS_WIFI_PASSWORD_KEY "password"

esp_err_t save_wifi_credentials(const char* ssid, const char* password)
{
    if (!ssid) {
        return ESP_ERR_INVALID_ARG;
    }

    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open(NVS_WIFI_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS handle: %s", esp_err_to_name(ret));
        return ret;
    }

    // 保存SSID
    ret = nvs_set_str(nvs_handle, NVS_WIFI_SSID_KEY, ssid);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to save SSID: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle);
        return ret;
    }

    // 保存密码
    const char* pwd = password ? password : "";
    ret = nvs_set_str(nvs_handle, NVS_WIFI_PASSWORD_KEY, pwd);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to save password: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle);
        return ret;
    }

    // 提交更改
    ret = nvs_commit(nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to commit NVS: %s", esp_err_to_name(ret));
    } else {
        ESP_LOGI(TAG, "WiFi credentials saved successfully");
    }

    nvs_close(nvs_handle);
    return ret;
}

esp_err_t load_wifi_credentials(char* ssid, char* password)
{
    if (!ssid || !password) {
        return ESP_ERR_INVALID_ARG;
    }

    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open(NVS_WIFI_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS handle: %s", esp_err_to_name(ret));
        return ret;
    }

    // 加载SSID
    size_t ssid_len = 33;
    ret = nvs_get_str(nvs_handle, NVS_WIFI_SSID_KEY, ssid, &ssid_len);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to load SSID: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle);
        return ret;
    }

    // 加载密码
    size_t password_len = 65;
    ret = nvs_get_str(nvs_handle, NVS_WIFI_PASSWORD_KEY, password, &password_len);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to load password: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle);
        return ret;
    }

    nvs_close(nvs_handle);
    ESP_LOGI(TAG, "WiFi credentials loaded successfully");
    return ESP_OK;
}

esp_err_t clear_wifi_credentials(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open(NVS_WIFI_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS handle: %s", esp_err_to_name(ret));
        return ret;
    }

    // 清除SSID和密码
    nvs_erase_key(nvs_handle, NVS_WIFI_SSID_KEY);
    nvs_erase_key(nvs_handle, NVS_WIFI_PASSWORD_KEY);

    ret = nvs_commit(nvs_handle);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "WiFi credentials cleared successfully");
    }

    nvs_close(nvs_handle);
    return ret;
}

/*---- 新屏幕创建函数的基本实现 ----*/

lv_obj_t* screen_create_password(void)
{
    ESP_LOGI(TAG, "Creating password verification screen...");

    // 检查是否被锁定
    if (is_password_locked()) {
        ESP_LOGW(TAG, "Password verification is locked");
    }

    // 创建屏幕对象
    lv_obj_t* screen = lv_obj_create(NULL);
    if (!screen) {
        ESP_LOGE(TAG, "Failed to create password screen object");
        return NULL;
    }

    // 设置屏幕背景
    lv_obj_set_style_bg_color(screen, lv_color_hex(0x003366), 0);
    lv_obj_clear_flag(screen, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(screen, LV_SCROLLBAR_MODE_OFF);

    // 创建标题
    lv_obj_t* title = lv_label_create(screen);
    lv_label_set_text(title, "Password Verification");
    lv_obj_set_style_text_color(title, lv_color_white(), 0);
    // 应用英文字体
#if LV_FONT_MONTSERRAT_18
    lv_obj_set_style_text_font(title, &lv_font_montserrat_18, 0);
#elif LV_FONT_MONTSERRAT_16
    lv_obj_set_style_text_font(title, &lv_font_montserrat_16, 0);
#elif LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(title, &lv_font_montserrat_14, 0);
#else
    lv_obj_set_style_text_font(title, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 20);

    // 创建状态提示文本
    lv_obj_t* status_label = lv_label_create(screen);
    if (is_password_locked()) {
        lv_label_set_text(status_label, "Password locked, try again later");
        lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
    } else {
        uint8_t remaining = get_remaining_attempts();
        lv_label_set_text_fmt(status_label, "Enter 4-digit password (%d attempts left)", remaining);
        lv_obj_set_style_text_color(status_label, lv_color_white(), 0);
    }
    // 应用英文字体
#if LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(status_label, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(status_label, &lv_font_montserrat_12, 0);
#else
    lv_obj_set_style_text_font(status_label, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(status_label, LV_ALIGN_TOP_MID, 0, 60);

    // 创建密码输入显示框
    lv_obj_t* password_display = lv_label_create(screen);
    lv_label_set_text(password_display, "Password: ****");  // 4个星号表示4位密码
    lv_obj_set_style_text_color(password_display, lv_color_white(), 0);
    // 应用字体（使用条件编译）
#if LV_FONT_MONTSERRAT_18
    lv_obj_set_style_text_font(password_display, &lv_font_montserrat_18, 0);
#elif LV_FONT_MONTSERRAT_16
    lv_obj_set_style_text_font(password_display, &lv_font_montserrat_16, 0);
#elif LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(password_display, &lv_font_montserrat_14, 0);
#else
    lv_obj_set_style_text_font(password_display, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_set_style_bg_color(password_display, lv_color_hex(0x444444), 0);
    lv_obj_set_style_bg_opa(password_display, LV_OPA_COVER, 0);
    lv_obj_set_style_pad_all(password_display, 10, 0);
    lv_obj_set_style_radius(password_display, 5, 0);
    lv_obj_align(password_display, LV_ALIGN_TOP_MID, 0, 100);

    // 保存密码显示框的引用（用于更新显示）
    lv_obj_set_user_data(screen, password_display);

    // 创建roller容器
    lv_obj_t* roller_container = lv_obj_create(screen);
    lv_obj_set_size(roller_container, 200, 100);
    lv_obj_align(roller_container, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_bg_color(roller_container, lv_color_hex(0x333333), 0);
    lv_obj_set_style_border_width(roller_container, 1, 0);
    lv_obj_set_style_border_color(roller_container, lv_color_white(), 0);
    lv_obj_clear_flag(roller_container, LV_OBJ_FLAG_SCROLLABLE);

    // 数字选项：0-9
    const char* digit_options = "0\n1\n2\n3\n4\n5\n6\n7\n8\n9";

    // 创建4个roller用于4位密码，默认值为"0001"
    const char* default_password = "0001";
    for (int i = 0; i < 4; i++) {
        lv_obj_t* roller = lv_roller_create(roller_container);
        lv_roller_set_options(roller, digit_options, LV_ROLLER_MODE_INFINITE);
        lv_obj_set_size(roller, 40, 80);
        lv_obj_align(roller, LV_ALIGN_LEFT_MID, 10 + i * 45, 0);
        lv_obj_set_style_bg_color(roller, lv_color_hex(0x444444), 0);
        lv_obj_set_style_text_color(roller, lv_color_white(), 0);
        lv_obj_set_style_text_font(roller, LV_FONT_DEFAULT, 0);

        // 设置默认值：前3个roller为0，第4个roller为1（密码"0001"）
        if (i < 3) {
            lv_roller_set_selected(roller, 0, LV_ANIM_OFF);  // 设置为"0"
        } else {
            lv_roller_set_selected(roller, 1, LV_ANIM_OFF);  // 设置为"1"
        }

        // 添加事件处理器
        lv_obj_add_event_cb(roller, screen_password_roller_handler, LV_EVENT_VALUE_CHANGED, screen);
    }

    // 初始化密码管理器的默认值
    g_screen_manager.password_manager.input_length = 4;
    strncpy(g_screen_manager.password_manager.input_buffer, default_password, 4);
    g_screen_manager.password_manager.input_buffer[4] = '\0';

    // 创建控制按钮容器
    lv_obj_t* btn_container = lv_obj_create(screen);
    lv_obj_set_size(btn_container, 200, 50);
    lv_obj_align(btn_container, LV_ALIGN_BOTTOM_MID, 0, -20);
    lv_obj_set_style_bg_opa(btn_container, LV_OPA_TRANSP, 0);
    lv_obj_set_style_border_opa(btn_container, LV_OPA_TRANSP, 0);
    lv_obj_clear_flag(btn_container, LV_OBJ_FLAG_SCROLLABLE);

    // 创建确认按钮
    lv_obj_t* confirm_btn = lv_btn_create(btn_container);
    lv_obj_set_size(confirm_btn, 80, 40);
    lv_obj_align(confirm_btn, LV_ALIGN_LEFT_MID, 10, 0);
    lv_obj_set_style_bg_color(confirm_btn, lv_color_hex(0x00AA00), 0);  // 绿色
    lv_obj_add_event_cb(confirm_btn, screen_password_control_handler, LV_EVENT_CLICKED, (void*)1);  // 1 = confirm

    lv_obj_t* confirm_label = lv_label_create(confirm_btn);
    lv_label_set_text(confirm_label, "Confirm");
    lv_obj_center(confirm_label);

    // 创建取消按钮
    lv_obj_t* cancel_btn = lv_btn_create(btn_container);
    lv_obj_set_size(cancel_btn, 80, 40);
    lv_obj_align(cancel_btn, LV_ALIGN_RIGHT_MID, -10, 0);
    lv_obj_set_style_bg_color(cancel_btn, lv_color_hex(0xAA0000), 0);  // 红色
    lv_obj_add_event_cb(cancel_btn, screen_password_control_handler, LV_EVENT_CLICKED, (void*)0);  // 0 = cancel

    lv_obj_t* cancel_label = lv_label_create(cancel_btn);
    lv_label_set_text(cancel_label, "Cancel");
    lv_obj_center(cancel_label);

    // 应用英文字体到按钮标签
#if LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(confirm_label, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_font(cancel_label, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(confirm_label, &lv_font_montserrat_12, 0);
    lv_obj_set_style_text_font(cancel_label, &lv_font_montserrat_12, 0);
#endif

    ESP_LOGI(TAG, "Password verification screen created successfully");
    return screen;
}

lv_obj_t* screen_create_wifi_config(void)
{
    ESP_LOGI(TAG, "Creating WiFi configuration screen...");

    // 创建屏幕对象
    lv_obj_t* screen = lv_obj_create(NULL);
    if (!screen) {
        ESP_LOGE(TAG, "Failed to create WiFi config screen object");
        return NULL;
    }

    // 设置屏幕背景
    lv_obj_set_style_bg_color(screen, lv_color_hex(0x003366), 0);
    lv_obj_clear_flag(screen, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(screen, LV_SCROLLBAR_MODE_OFF);

    // 创建标题
    lv_obj_t* title = lv_label_create(screen);
    lv_label_set_text(title, "WiFi Configuration");
    lv_obj_set_style_text_color(title, lv_color_white(), 0);
    // 应用英文字体
#if LV_FONT_MONTSERRAT_18
    lv_obj_set_style_text_font(title, &lv_font_montserrat_18, 0);
#elif LV_FONT_MONTSERRAT_16
    lv_obj_set_style_text_font(title, &lv_font_montserrat_16, 0);
#elif LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(title, &lv_font_montserrat_14, 0);
#else
    lv_obj_set_style_text_font(title, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 15);

    // 添加双击事件处理器到标题
    lv_obj_add_flag(title, LV_OBJ_FLAG_CLICKABLE);  // 使标题可点击
    lv_obj_add_event_cb(title, screen_wifi_config_title_handler, LV_EVENT_CLICKED, NULL);

    // 创建状态标签
    lv_obj_t* status_label = lv_label_create(screen);
    lv_label_set_text(status_label, "Scanning WiFi networks...");
    lv_obj_set_style_text_color(status_label, lv_color_hex(0xFFFF00), 0);  // 黄色
    // 应用英文字体
#if LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(status_label, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(status_label, &lv_font_montserrat_12, 0);
#else
    lv_obj_set_style_text_font(status_label, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(status_label, LV_ALIGN_TOP_MID, 0, 50);

    // 创建WiFi列表容器
    lv_obj_t* list_container = lv_obj_create(screen);
    lv_obj_set_size(list_container, 220, 180);
    lv_obj_align(list_container, LV_ALIGN_CENTER, 0, -10);
    lv_obj_set_style_bg_color(list_container, lv_color_hex(0x444444), 0);
    lv_obj_set_style_bg_opa(list_container, LV_OPA_80, 0);
    lv_obj_set_style_border_width(list_container, 1, 0);
    lv_obj_set_style_border_color(list_container, lv_color_white(), 0);
    lv_obj_set_style_radius(list_container, 5, 0);
    lv_obj_set_style_pad_all(list_container, 5, 0);

    // 创建WiFi列表
    lv_obj_t* wifi_list = lv_list_create(list_container);
    lv_obj_set_size(wifi_list, lv_pct(100), lv_pct(100));
    lv_obj_center(wifi_list);
    lv_obj_set_style_bg_opa(wifi_list, LV_OPA_TRANSP, 0);
    lv_obj_set_style_border_opa(wifi_list, LV_OPA_TRANSP, 0);

    // 保存WiFi列表引用到屏幕用户数据
    lv_obj_set_user_data(screen, wifi_list);

    // 创建控制按钮容器
    lv_obj_t* btn_container = lv_obj_create(screen);
    lv_obj_set_size(btn_container, 220, 50);
    lv_obj_align(btn_container, LV_ALIGN_BOTTOM_MID, 0, -10);
    lv_obj_set_style_bg_opa(btn_container, LV_OPA_TRANSP, 0);
    lv_obj_set_style_border_opa(btn_container, LV_OPA_TRANSP, 0);
    lv_obj_clear_flag(btn_container, LV_OBJ_FLAG_SCROLLABLE);

    // 创建刷新按钮
    lv_obj_t* refresh_btn = lv_btn_create(btn_container);
    lv_obj_set_size(refresh_btn, 80, 40);
    lv_obj_align(refresh_btn, LV_ALIGN_LEFT_MID, 10, 0);
    lv_obj_set_style_bg_color(refresh_btn, lv_color_hex(0x0066CC), 0);  // 蓝色
    lv_obj_add_event_cb(refresh_btn, screen_wifi_config_control_handler, LV_EVENT_CLICKED, (void*)1);  // 1 = refresh

    lv_obj_t* refresh_label = lv_label_create(refresh_btn);
    lv_label_set_text(refresh_label, "Refresh");
    lv_obj_center(refresh_label);

    // 创建返回按钮
    lv_obj_t* back_btn = lv_btn_create(btn_container);
    lv_obj_set_size(back_btn, 80, 40);
    lv_obj_align(back_btn, LV_ALIGN_RIGHT_MID, -10, 0);
    lv_obj_set_style_bg_color(back_btn, lv_color_hex(0xAA0000), 0);  // 红色
    lv_obj_add_event_cb(back_btn, screen_wifi_config_control_handler, LV_EVENT_CLICKED, (void*)0);  // 0 = back

    lv_obj_t* back_label = lv_label_create(back_btn);
    lv_label_set_text(back_label, "Back");
    lv_obj_center(back_label);

    // 应用英文字体到按钮标签
#if LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(refresh_label, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_font(back_label, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(refresh_label, &lv_font_montserrat_12, 0);
    lv_obj_set_style_text_font(back_label, &lv_font_montserrat_12, 0);
#endif

    // 启动WiFi扫描
    esp_err_t ret = wifi_start_scan();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start WiFi scan: %s", esp_err_to_name(ret));
        lv_label_set_text(status_label, "WiFi scan failed");
        lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
    }

    ESP_LOGI(TAG, "WiFi configuration screen created successfully");
    return screen;
}

lv_obj_t* screen_create_wifi_password(const char* selected_ssid)
{
    ESP_LOGI(TAG, "Creating WiFi password input screen for SSID: %s", selected_ssid ? selected_ssid : "Unknown");

    // 创建屏幕对象
    lv_obj_t* screen = lv_obj_create(NULL);
    if (!screen) {
        ESP_LOGE(TAG, "Failed to create WiFi password screen object");
        return NULL;
    }

    // 设置屏幕背景
    lv_obj_set_style_bg_color(screen, lv_color_hex(0x003366), 0);
    lv_obj_clear_flag(screen, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(screen, LV_SCROLLBAR_MODE_OFF);

    // 创建标题
    lv_obj_t* title = lv_label_create(screen);
    lv_label_set_text(title, "WiFi Password");
    lv_obj_set_style_text_color(title, lv_color_white(), 0);
    // 应用英文字体
#if LV_FONT_MONTSERRAT_18
    lv_obj_set_style_text_font(title, &lv_font_montserrat_18, 0);
#elif LV_FONT_MONTSERRAT_16
    lv_obj_set_style_text_font(title, &lv_font_montserrat_16, 0);
#elif LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(title, &lv_font_montserrat_14, 0);
#else
    lv_obj_set_style_text_font(title, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 15);

    // 显示选中的SSID
    if (selected_ssid) {
        lv_obj_t* ssid_label = lv_label_create(screen);
        lv_label_set_text_fmt(ssid_label, "Network: %s", selected_ssid);
        lv_obj_set_style_text_color(ssid_label, lv_color_white(), 0);
        // 应用英文字体
#if LV_FONT_MONTSERRAT_14
        lv_obj_set_style_text_font(ssid_label, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_12
        lv_obj_set_style_text_font(ssid_label, &lv_font_montserrat_12, 0);
#else
        lv_obj_set_style_text_font(ssid_label, LV_FONT_DEFAULT, 0);
#endif
        lv_obj_align(ssid_label, LV_ALIGN_TOP_MID, 0, 50);
    }

    // 创建密码显示标签（独立一行，位置更高）
    lv_obj_t* password_display = lv_label_create(screen);
    lv_label_set_text(password_display, "Password: ");
    lv_obj_set_style_text_color(password_display, lv_color_white(), 0);
    lv_obj_set_style_bg_color(password_display, lv_color_hex(0x444444), 0);
    lv_obj_set_style_bg_opa(password_display, LV_OPA_COVER, 0);
    lv_obj_set_style_pad_all(password_display, 6, 0);
    lv_obj_set_style_radius(password_display, 4, 0);
#if LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(password_display, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(password_display, &lv_font_montserrat_12, 0);
#else
    lv_obj_set_style_text_font(password_display, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(password_display, LV_ALIGN_TOP_MID, 0, 85);  // 提高位置，给下面组件留出空间

    // 创建主容器（roller区域）
    lv_obj_t* main_container = lv_obj_create(screen);
    lv_obj_set_size(main_container, 240, 120);  // 进一步减小高度
    lv_obj_align(main_container, LV_ALIGN_TOP_MID, 0, 120);  // 紧贴密码显示下方
    lv_obj_set_style_bg_opa(main_container, LV_OPA_TRANSP, 0);
    lv_obj_set_style_border_opa(main_container, LV_OPA_TRANSP, 0);
    lv_obj_clear_flag(main_container, LV_OBJ_FLAG_SCROLLABLE);

    // 定义字符集
    const char* lowercase_options = "a\nb\nc\nd\ne\nf\ng\nh\ni\nj\nk\nl\nm\nn\no\np\nq\nr\ns\nt\nu\nv\nw\nx\ny\nz";
    const char* uppercase_options = "A\nB\nC\nD\nE\nF\nG\nH\nI\nJ\nK\nL\nM\nN\nO\nP\nQ\nR\nS\nT\nU\nV\nW\nX\nY\nZ";
    const char* digit_options = "0\n1\n2\n3\n4\n5\n6\n7\n8\n9";

    // 创建3个roller和对应的确认按钮
    const char* roller_options[] = {lowercase_options, uppercase_options, digit_options};
    const char* roller_labels[] = {"a-z", "A-Z", "0-9"};

    for (int i = 0; i < 3; i++) {
        // 创建roller标签
        lv_obj_t* label = lv_label_create(main_container);
        lv_label_set_text(label, roller_labels[i]);
        lv_obj_set_style_text_color(label, lv_color_white(), 0);
        lv_obj_align(label, LV_ALIGN_TOP_LEFT, 20 + i * 75, 5);  // 减少顶部间距

        // 创建roller（减小高度）
        lv_obj_t* roller = lv_roller_create(main_container);
        lv_roller_set_options(roller, roller_options[i], LV_ROLLER_MODE_INFINITE);
        lv_obj_set_size(roller, 50, 60);  // 减小高度从80到60
        lv_obj_align(roller, LV_ALIGN_TOP_LEFT, 10 + i * 75, 20);  // 减少顶部间距
        lv_obj_set_style_bg_color(roller, lv_color_hex(0x444444), 0);
        lv_obj_set_style_text_color(roller, lv_color_white(), 0);
        lv_obj_set_style_text_font(roller, LV_FONT_DEFAULT, 0);

        // 创建确认按钮（位置更靠近roller）
        lv_obj_t* confirm_btn = lv_btn_create(main_container);
        lv_obj_set_size(confirm_btn, 50, 25);  // 减小高度
        lv_obj_align(confirm_btn, LV_ALIGN_TOP_LEFT, 10 + i * 75, 85);  // 调整位置
        lv_obj_set_style_bg_color(confirm_btn, lv_color_hex(0x0066CC), 0);  // 蓝色
        lv_obj_add_event_cb(confirm_btn, screen_wifi_password_char_handler, LV_EVENT_CLICKED, (void*)(intptr_t)i);

        lv_obj_t* btn_label = lv_label_create(confirm_btn);
        lv_label_set_text(btn_label, "Add");
        lv_obj_center(btn_label);
#if LV_FONT_MONTSERRAT_12
        lv_obj_set_style_text_font(btn_label, &lv_font_montserrat_12, 0);
#else
        lv_obj_set_style_text_font(btn_label, LV_FONT_DEFAULT, 0);
#endif
    }

    // 初始化密码输入缓冲区
    memset(g_screen_manager.wifi_manager.input_password, 0, sizeof(g_screen_manager.wifi_manager.input_password));
    g_screen_manager.wifi_manager.input_password_length = 0;

    // 保存密码显示标签引用到屏幕用户数据
    lv_obj_set_user_data(screen, password_display);

    // 创建清除按钮
    lv_obj_t* clear_btn = lv_btn_create(screen);
    lv_obj_set_size(clear_btn, 60, 30);
    lv_obj_align(clear_btn, LV_ALIGN_TOP_RIGHT, -10, 110);  // 与密码显示标签同一行
    lv_obj_set_style_bg_color(clear_btn, lv_color_hex(0xFF6600), 0);  // 橙色
    lv_obj_add_event_cb(clear_btn, screen_wifi_password_clear_handler, LV_EVENT_CLICKED, NULL);

    lv_obj_t* clear_label = lv_label_create(clear_btn);
    lv_label_set_text(clear_label, "Back");  // 改为"Back"表示退格功能
    lv_obj_center(clear_label);
#if LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(clear_label, &lv_font_montserrat_12, 0);
#else
    lv_obj_set_style_text_font(clear_label, LV_FONT_DEFAULT, 0);
#endif

    // 创建状态标签（用于显示连接进度）
    lv_obj_t* status_label = lv_label_create(screen);
    lv_label_set_text(status_label, "");
    lv_obj_set_style_text_color(status_label, lv_color_white(), 0);
    // 应用英文字体
#if LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(status_label, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(status_label, &lv_font_montserrat_12, 0);
#else
    lv_obj_set_style_text_font(status_label, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(status_label, LV_ALIGN_BOTTOM_MID, 0, -70);  // 调整位置避免与按钮重叠

    // 创建控制按钮容器
    lv_obj_t* btn_container = lv_obj_create(screen);
    lv_obj_set_size(btn_container, 220, 50);
    lv_obj_align(btn_container, LV_ALIGN_BOTTOM_MID, 0, -10);
    lv_obj_set_style_bg_opa(btn_container, LV_OPA_TRANSP, 0);
    lv_obj_set_style_border_opa(btn_container, LV_OPA_TRANSP, 0);
    lv_obj_clear_flag(btn_container, LV_OBJ_FLAG_SCROLLABLE);

    // 创建连接按钮
    lv_obj_t* connect_btn = lv_btn_create(btn_container);
    lv_obj_set_size(connect_btn, 80, 40);
    lv_obj_align(connect_btn, LV_ALIGN_LEFT_MID, 10, 0);
    lv_obj_set_style_bg_color(connect_btn, lv_color_hex(0x00AA00), 0);  // 绿色
    lv_obj_add_event_cb(connect_btn, screen_wifi_password_control_handler, LV_EVENT_CLICKED, (void*)1);  // 1 = connect

    lv_obj_t* connect_label = lv_label_create(connect_btn);
    lv_label_set_text(connect_label, "Connect");
    lv_obj_center(connect_label);

    // 创建取消按钮
    lv_obj_t* cancel_btn = lv_btn_create(btn_container);
    lv_obj_set_size(cancel_btn, 80, 40);
    lv_obj_align(cancel_btn, LV_ALIGN_RIGHT_MID, -10, 0);
    lv_obj_set_style_bg_color(cancel_btn, lv_color_hex(0xAA0000), 0);  // 红色
    lv_obj_add_event_cb(cancel_btn, screen_wifi_password_control_handler, LV_EVENT_CLICKED, (void*)0);  // 0 = cancel

    lv_obj_t* cancel_label = lv_label_create(cancel_btn);
    lv_label_set_text(cancel_label, "Cancel");
    lv_obj_center(cancel_label);

    // 应用英文字体到按钮标签
#if LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(connect_label, &lv_font_montserrat_14, 0);
    lv_obj_set_style_text_font(cancel_label, &lv_font_montserrat_14, 0);
#elif LV_FONT_MONTSERRAT_12
    lv_obj_set_style_text_font(connect_label, &lv_font_montserrat_12, 0);
    lv_obj_set_style_text_font(cancel_label, &lv_font_montserrat_12, 0);
#endif

    ESP_LOGI(TAG, "WiFi password input screen created successfully");
    return screen;
}

// 创建增强版WiFi密码输入屏幕（支持自定义SSID）
lv_obj_t* screen_create_wifi_password_enhanced(const char* ssid)
{
    ESP_LOGI(TAG, "Creating enhanced WiFi password screen (custom network mode: %s)...",
             ssid ? "false" : "true");

    // 设置自定义网络模式标志
    g_screen_manager.wifi_manager.is_custom_network_mode = (ssid == NULL);

    // 如果是自定义模式，初始化自定义SSID缓冲区
    if (g_screen_manager.wifi_manager.is_custom_network_mode) {
        memset(g_screen_manager.wifi_manager.custom_ssid, 0, sizeof(g_screen_manager.wifi_manager.custom_ssid));
        g_screen_manager.wifi_manager.custom_ssid_length = 0;
    } else {
        // 普通模式，保存传入的SSID
        strncpy(g_screen_manager.wifi_manager.saved_ssid, ssid, sizeof(g_screen_manager.wifi_manager.saved_ssid) - 1);
        g_screen_manager.wifi_manager.saved_ssid[sizeof(g_screen_manager.wifi_manager.saved_ssid) - 1] = '\0';
    }

    // 创建屏幕对象
    lv_obj_t* screen = lv_obj_create(NULL);
    if (!screen) {
        ESP_LOGE(TAG, "Failed to create enhanced WiFi password screen object");
        return NULL;
    }

    // 设置屏幕背景
    lv_obj_set_style_bg_color(screen, lv_color_hex(0x003366), 0);
    lv_obj_clear_flag(screen, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(screen, LV_SCROLLBAR_MODE_OFF);

    // 创建标题
    lv_obj_t* title = lv_label_create(screen);
    if (g_screen_manager.wifi_manager.is_custom_network_mode) {
        lv_label_set_text(title, "Custom Network Setup");
    } else {
        lv_label_set_text_fmt(title, "Connect to: %s", ssid);
    }
    lv_obj_set_style_text_color(title, lv_color_white(), 0);
#if LV_FONT_MONTSERRAT_16
    lv_obj_set_style_text_font(title, &lv_font_montserrat_16, 0);
#elif LV_FONT_MONTSERRAT_14
    lv_obj_set_style_text_font(title, &lv_font_montserrat_14, 0);
#else
    lv_obj_set_style_text_font(title, LV_FONT_DEFAULT, 0);
#endif
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 10);

    // 如果是自定义网络模式，创建SSID输入区域
    if (g_screen_manager.wifi_manager.is_custom_network_mode) {
        // SSID输入标签
        lv_obj_t* ssid_label = lv_label_create(screen);
        lv_label_set_text(ssid_label, "Network Name (SSID):");
        lv_obj_set_style_text_color(ssid_label, lv_color_white(), 0);
        lv_obj_align(ssid_label, LV_ALIGN_TOP_LEFT, 10, 40);

        // SSID显示区域
        lv_obj_t* ssid_display = lv_label_create(screen);
        lv_label_set_text(ssid_display, "");
        lv_obj_set_style_text_color(ssid_display, lv_color_hex(0x00FF00), 0);  // 绿色
        lv_obj_set_style_bg_color(ssid_display, lv_color_hex(0x222222), 0);
        lv_obj_set_style_bg_opa(ssid_display, LV_OPA_COVER, 0);
        lv_obj_set_style_pad_all(ssid_display, 5, 0);
        lv_obj_set_style_radius(ssid_display, 3, 0);
        lv_obj_set_size(ssid_display, 200, 25);
        lv_obj_align(ssid_display, LV_ALIGN_TOP_LEFT, 10, 65);

        // 创建SSID字符输入roller（简化版，只有字母和数字）
        lv_obj_t* ssid_roller = lv_roller_create(screen);
        lv_roller_set_options(ssid_roller,
            "A\nB\nC\nD\nE\nF\nG\nH\nI\nJ\nK\nL\nM\nN\nO\nP\nQ\nR\nS\nT\nU\nV\nW\nX\nY\nZ\n"
            "a\nb\nc\nd\ne\nf\ng\nh\ni\nj\nk\nl\nm\nn\no\np\nq\nr\ns\nt\nu\nv\nw\nx\ny\nz\n"
            "0\n1\n2\n3\n4\n5\n6\n7\n8\n9\n-\n_", LV_ROLLER_MODE_INFINITE);
        lv_obj_set_size(ssid_roller, 60, 80);
        lv_obj_align(ssid_roller, LV_ALIGN_TOP_LEFT, 10, 95);
        lv_roller_set_visible_row_count(ssid_roller, 4);
        lv_obj_add_event_cb(ssid_roller, screen_wifi_password_roller_handler, LV_EVENT_VALUE_CHANGED, screen);

        // SSID字符添加按钮
        lv_obj_t* ssid_add_btn = lv_btn_create(screen);
        lv_obj_set_size(ssid_add_btn, 50, 25);
        lv_obj_align(ssid_add_btn, LV_ALIGN_TOP_LEFT, 80, 110);
        lv_obj_set_style_bg_color(ssid_add_btn, lv_color_hex(0x0066CC), 0);
        lv_obj_add_event_cb(ssid_add_btn, screen_wifi_custom_ssid_char_handler, LV_EVENT_CLICKED, NULL);

        lv_obj_t* ssid_add_label = lv_label_create(ssid_add_btn);
        lv_label_set_text(ssid_add_label, "Add");
        lv_obj_center(ssid_add_label);
    }

    // 密码输入区域（根据是否有SSID输入调整位置）
    int password_y_offset = g_screen_manager.wifi_manager.is_custom_network_mode ? 180 : 50;

    // 密码输入标签
    lv_obj_t* password_label = lv_label_create(screen);
    lv_label_set_text(password_label, "WiFi Password:");
    lv_obj_set_style_text_color(password_label, lv_color_white(), 0);
    lv_obj_align(password_label, LV_ALIGN_TOP_LEFT, 10, password_y_offset);

    // 密码显示区域
    lv_obj_t* password_display = lv_label_create(screen);
    lv_label_set_text(password_display, "");
    lv_obj_set_style_text_color(password_display, lv_color_hex(0x00FF00), 0);
    lv_obj_set_style_bg_color(password_display, lv_color_hex(0x222222), 0);
    lv_obj_set_style_bg_opa(password_display, LV_OPA_COVER, 0);
    lv_obj_set_style_pad_all(password_display, 5, 0);
    lv_obj_set_style_radius(password_display, 3, 0);
    lv_obj_set_size(password_display, 150, 25);
    lv_obj_align(password_display, LV_ALIGN_TOP_LEFT, 10, password_y_offset + 25);

    ESP_LOGI(TAG, "Enhanced WiFi password screen created successfully (custom mode: %s)",
             g_screen_manager.wifi_manager.is_custom_network_mode ? "true" : "false");
    return screen;
}

/*---- 密码验证屏幕事件处理器实现 ----*/

void screen_password_roller_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t* roller = lv_event_get_target(e);
    lv_obj_t* screen = (lv_obj_t*)lv_event_get_user_data(e);

    if (code == LV_EVENT_VALUE_CHANGED && screen && roller) {
        // 检查是否被锁定
        if (is_password_locked()) {
            ESP_LOGW(TAG, "Password input blocked - system is locked");
            return;
        }

        // 记录roller变化（用于调试）
        uint16_t selected = lv_roller_get_selected(roller);
        ESP_LOGD(TAG, "Password roller changed to: %u", (unsigned int)selected);

        // 更新密码显示
        update_password_verification_display(screen);
    }
}

// 更新密码验证显示函数
void update_password_verification_display(lv_obj_t* screen)
{
    if (!screen) return;

    lv_obj_t* password_display = (lv_obj_t*)lv_obj_get_user_data(screen);
    if (!password_display) return;

    // 找到roller容器
    lv_obj_t* roller_container = NULL;
    for (uint32_t i = 0; i < lv_obj_get_child_cnt(screen); i++) {
        lv_obj_t* child = lv_obj_get_child(screen, i);
        if (lv_obj_get_child_cnt(child) == 4) {  // 包含4个roller的容器
            roller_container = child;
            break;
        }
    }

    if (!roller_container) return;

    // 构建密码字符串
    char password[5] = {0};  // 4位密码 + 结束符

    for (int i = 0; i < 4; i++) {
        lv_obj_t* roller = lv_obj_get_child(roller_container, i);
        if (roller) {
            uint16_t selected = lv_roller_get_selected(roller);
            password[i] = '0' + selected;  // 数字0-9对应选项0-9
        }
    }

    // 更新显示（用*号隐藏密码）
    lv_label_set_text(password_display, "Password: ****");

    // 保存实际密码到全局变量
    g_screen_manager.password_manager.input_length = 4;
    strncpy(g_screen_manager.password_manager.input_buffer, password, 4);
    g_screen_manager.password_manager.input_buffer[4] = '\0';
}

void screen_password_control_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    int action = (int)lv_event_get_user_data(e);

    if (code == LV_EVENT_CLICKED) {
        if (action == 0) {
            // 取消按钮
            ESP_LOGI(TAG, "Password verification cancelled");
            // 清除输入并返回初始屏幕
            reset_password_attempts();
            screen_manager_switch_to(SCREEN_INITIAL);

        } else if (action == 1) {
            // 确认按钮
            ESP_LOGI(TAG, "Password verification confirm pressed");

            // 检查是否被锁定
            if (is_password_locked()) {
                ESP_LOGW(TAG, "Password verification blocked - system is locked");
                return;
            }

            // 确保密码显示是最新的
            lv_obj_t* current_screen = lv_scr_act();
            update_password_verification_display(current_screen);

            // 检查密码长度
            if (g_screen_manager.password_manager.input_length != 4) {
                ESP_LOGW(TAG, "Password length incorrect: %u", (unsigned int)g_screen_manager.password_manager.input_length);
                return;
            }

            // 验证密码
            if (validate_admin_password(g_screen_manager.password_manager.input_buffer)) {
                ESP_LOGI(TAG, "Password verification successful");
                reset_password_attempts();

                // 跳转到WiFi配置屏幕
                screen_manager_switch_to(SCREEN_WIFI_CONFIG);

            } else {
                ESP_LOGW(TAG, "Password verification failed");
                increment_failed_attempts();

                // 清除输入
                g_screen_manager.password_manager.input_length = 0;
                memset(g_screen_manager.password_manager.input_buffer, 0,
                       sizeof(g_screen_manager.password_manager.input_buffer));

                // 检查是否被锁定
                if (is_password_locked()) {
                    ESP_LOGW(TAG, "Password verification locked due to too many failed attempts");
                    // 显示锁定消息并返回初始屏幕
                    screen_manager_switch_to(SCREEN_INITIAL);
                } else {
                    // 显示错误消息并重新创建屏幕
                    ESP_LOGI(TAG, "Recreating password screen with error message");
                    screen_manager_switch_to(SCREEN_PASSWORD);
                }
            }
        }
    }
}
/*---- WiFi配置屏幕事件处理器实现 ----*/

// WiFi配置屏幕标题双击事件处理器
void screen_wifi_config_title_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED) {
        uint32_t current_time = lv_tick_get();

        // 检查是否为双击（无时间限制，任何速度都可以）
        g_screen_manager.title_click_count++;

        ESP_LOGD(TAG, "Title clicked, count: %lu", (unsigned long)g_screen_manager.title_click_count);

        // 如果是第二次点击，触发隐藏网络配置
        if (g_screen_manager.title_click_count >= 2) {
            ESP_LOGI(TAG, "Double-click detected on WiFi Configuration title - opening custom network mode");

            // 重置点击计数
            g_screen_manager.title_click_count = 0;

            // 清理可能存在的旧WiFi密码屏幕对象
            if (g_screen_manager.screen_objects[SCREEN_WIFI_PASSWORD]) {
                ESP_LOGI(TAG, "Cleaning up existing WiFi password screen before creating custom network screen");
                safe_delete_screen(&g_screen_manager.screen_objects[SCREEN_WIFI_PASSWORD]);
            }

            // 创建增强版WiFi密码输入屏幕（自定义网络模式）
            lv_obj_t* custom_screen = screen_create_wifi_password_enhanced(NULL);  // NULL表示自定义模式
            if (custom_screen) {
                // 手动切换到密码屏幕
                g_screen_manager.previous_screen = g_screen_manager.current_screen;
                g_screen_manager.current_screen = SCREEN_WIFI_PASSWORD;
                g_screen_manager.screen_objects[SCREEN_WIFI_PASSWORD] = custom_screen;
                lv_scr_load(custom_screen);
                ESP_LOGI(TAG, "Switched to custom network configuration screen");
            } else {
                ESP_LOGE(TAG, "Failed to create custom network configuration screen");
            }
        } else {
            // 第一次点击，记录时间（虽然没有时间限制，但保留用于调试）
            g_screen_manager.last_title_click_time = current_time;
            ESP_LOGD(TAG, "First click on title, waiting for potential second click");
        }
    }
}

void screen_wifi_config_list_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t* btn = lv_event_get_target(e);

    if (code == LV_EVENT_CLICKED) {
        // 获取选中的WiFi SSID
        lv_obj_t* label = lv_obj_get_child(btn, 0);
        if (label) {
            const char* ssid_text = lv_label_get_text(label);
            if (ssid_text) {
                // 提取SSID（去掉信号强度和加密图标）
                char ssid[33] = {0};
                const char* space_pos = strchr(ssid_text, ' ');
                if (space_pos) {
                    size_t ssid_len = space_pos - ssid_text;
                    if (ssid_len < sizeof(ssid)) {
                        strncpy(ssid, ssid_text, ssid_len);
                        ssid[ssid_len] = '\0';
                    }
                } else {
                    strncpy(ssid, ssid_text, sizeof(ssid) - 1);
                }

                ESP_LOGI(TAG, "WiFi selected: %s", ssid);

                // 检查是否是开放网络
                bool is_open = (strstr(ssid_text, "[Open]") != NULL);

                if (is_open) {
                    // 开放网络，直接连接
                    ESP_LOGI(TAG, "Connecting to open network: %s", ssid);
                    esp_err_t ret = wifi_connect(ssid, NULL);
                    if (ret == ESP_OK) {
                        // 保存WiFi凭据
                        save_wifi_credentials(ssid, "");
                        // 返回初始屏幕并显示连接状态
                        screen_manager_switch_to(SCREEN_INITIAL);
                    } else {
                        ESP_LOGE(TAG, "Failed to connect to open network");
                    }
                } else {
                    // 加密网络，跳转到密码输入屏幕
                    // 保存选中的SSID到全局变量
                    strncpy(g_screen_manager.wifi_manager.saved_ssid, ssid, sizeof(g_screen_manager.wifi_manager.saved_ssid) - 1);

                    // 清理可能存在的旧WiFi密码屏幕对象
                    if (g_screen_manager.screen_objects[SCREEN_WIFI_PASSWORD]) {
                        ESP_LOGI(TAG, "Cleaning up existing WiFi password screen before creating new one");
                        safe_delete_screen(&g_screen_manager.screen_objects[SCREEN_WIFI_PASSWORD]);
                    }

                    // 创建WiFi密码输入屏幕
                    lv_obj_t* password_screen = screen_create_wifi_password(ssid);
                    if (password_screen) {
                        // 手动切换到密码屏幕
                        g_screen_manager.previous_screen = g_screen_manager.current_screen;
                        g_screen_manager.current_screen = SCREEN_WIFI_PASSWORD;
                        g_screen_manager.screen_objects[SCREEN_WIFI_PASSWORD] = password_screen;
                        lv_scr_load(password_screen);
                        ESP_LOGI(TAG, "Switched to WiFi password screen for: %s", ssid);
                    } else {
                        ESP_LOGE(TAG, "Failed to create WiFi password screen for: %s", ssid);
                    }
                }
            }
        }
    }
}

void screen_wifi_config_control_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    int action = (int)lv_event_get_user_data(e);

    if (code == LV_EVENT_CLICKED) {
        if (action == 0) {
            // 返回按钮
            ESP_LOGI(TAG, "WiFi config back button pressed");
            screen_manager_switch_to(SCREEN_INITIAL);

        } else if (action == 1) {
            // 刷新按钮
            ESP_LOGI(TAG, "WiFi config refresh button pressed");

            // 清除当前WiFi列表
            lv_obj_t* current_screen = lv_scr_act();
            lv_obj_t* wifi_list = (lv_obj_t*)lv_obj_get_user_data(current_screen);
            if (wifi_list) {
                lv_obj_clean(wifi_list);
            }

            // 更新状态标签
            lv_obj_t* status_label = lv_obj_get_child(current_screen, 1);  // 第二个子对象是状态标签
            if (status_label) {
                lv_label_set_text(status_label, "Refreshing WiFi networks...");
                lv_obj_set_style_text_color(status_label, lv_color_hex(0xFFFF00), 0);  // 黄色
            }

            // 重新启动WiFi扫描
            esp_err_t ret = wifi_start_scan();
            if (ret != ESP_OK) {
                ESP_LOGE(TAG, "Failed to refresh WiFi scan: %s", esp_err_to_name(ret));
                if (status_label) {
                    lv_label_set_text(status_label, "WiFi scan failed");
                    lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
                }
            }
        }
    }
}

// WiFi扫描结果更新函数（需要在WiFi事件回调中调用）
void update_wifi_list_display(void)
{
    lv_obj_t* current_screen = lv_scr_act();
    if (g_screen_manager.current_screen != SCREEN_WIFI_CONFIG) {
        return;  // 不在WiFi配置屏幕，不更新
    }

    lv_obj_t* wifi_list = (lv_obj_t*)lv_obj_get_user_data(current_screen);
    if (!wifi_list) {
        ESP_LOGE(TAG, "WiFi list not found");
        return;
    }

    // 清除现有列表
    lv_obj_clean(wifi_list);

    // 获取扫描结果
    wifi_scan_result_t scan_result;
    esp_err_t ret = wifi_get_scan_results(&scan_result);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get WiFi scan results");
        return;
    }

    // 更新状态标签
    lv_obj_t* status_label = lv_obj_get_child(current_screen, 1);
    if (status_label) {
        if (scan_result.ap_count > 0) {
            lv_label_set_text_fmt(status_label, "Found %u WiFi networks", (unsigned int)scan_result.ap_count);
            lv_obj_set_style_text_color(status_label, lv_color_hex(0x00FF00), 0);  // 绿色
        } else {
            lv_label_set_text(status_label, "No WiFi networks found");
            lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
        }
    }

    // 添加WiFi网络到列表
    for (int i = 0; i < scan_result.ap_count && i < 10; i++) {  // 最多显示10个
        wifi_ap_info_t* ap = &scan_result.ap_list[i];

        // 创建列表项
        lv_obj_t* btn = lv_list_add_btn(wifi_list, NULL, "");
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x555555), 0);
        lv_obj_set_style_text_color(btn, lv_color_white(), 0);
        lv_obj_add_event_cb(btn, screen_wifi_config_list_handler, LV_EVENT_CLICKED, NULL);

        // 创建WiFi信息文本 - 使用简单安全的格式
        char wifi_text[96];  // 足够的缓冲区大小
        memset(wifi_text, 0, sizeof(wifi_text));

        // 确保SSID字符串安全（已经通过sanitize_ssid_string处理过）
        const char* security_text = ap->is_open ? "[Open]" : "[Secured]";
        const char* signal_text;

        // 根据信号强度选择文本
        if (ap->rssi > -50) {
            signal_text = "[Strong]";
        } else if (ap->rssi > -70) {
            signal_text = "[Medium]";
        } else {
            signal_text = "[Weak]";
        }

        // 使用安全的字符串格式化，确保不会溢出
        int ret = snprintf(wifi_text, sizeof(wifi_text) - 1, "%s %s %s",
                          ap->ssid, signal_text, security_text);

        // 确保字符串正确终止
        if (ret >= 0 && ret < sizeof(wifi_text)) {
            wifi_text[ret] = '\0';
        } else {
            wifi_text[sizeof(wifi_text) - 1] = '\0';
        }

        // 额外的安全检查：确保没有控制字符
        for (int j = 0; wifi_text[j] != '\0'; j++) {
            if (wifi_text[j] < 32 && wifi_text[j] != '\0') {
                wifi_text[j] = '?';
            }
        }

        // 设置按钮文本
        lv_obj_t* label = lv_obj_get_child(btn, 0);
        if (label) {
            lv_label_set_text(label, wifi_text);
            // 应用中文字体
#if defined(CONFIG_LV9_FONT_SOURCE_HAN_SANS_SC_14_CJK) || defined(LV_FONT_SOURCE_HAN_SANS_SC_14_CJK)
            lv_obj_set_style_text_font(label, &lv_font_source_han_sans_sc_14_cjk, 0);
#elif LV_FONT_MONTSERRAT_14
            lv_obj_set_style_text_font(label, &lv_font_montserrat_14, 0);
#endif
        }
    }

    // 释放扫描结果内存
    wifi_free_scan_results(&scan_result);

    ESP_LOGI(TAG, "WiFi list display updated with %u networks", (unsigned int)scan_result.ap_count);
}
// 自定义SSID字符添加事件处理器
void screen_wifi_custom_ssid_char_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED) {
        // 获取当前屏幕
        lv_obj_t* current_screen = lv_scr_act();
        if (!current_screen) {
            ESP_LOGE(TAG, "No current screen found");
            return;
        }

        // 查找SSID roller（第一个roller是SSID roller）
        lv_obj_t* ssid_roller = NULL;
        uint32_t child_count = lv_obj_get_child_cnt(current_screen);
        for (uint32_t i = 0; i < child_count; i++) {
            lv_obj_t* child = lv_obj_get_child(current_screen, i);
            if (lv_obj_check_type(child, &lv_roller_class)) {
                ssid_roller = child;
                break;  // 第一个roller就是SSID roller
            }
        }

        if (!ssid_roller) {
            ESP_LOGE(TAG, "SSID roller not found");
            return;
        }

        // 获取选中的字符
        uint16_t selected = lv_roller_get_selected(ssid_roller);
        const char* options = lv_roller_get_options(ssid_roller);

        // 解析选中的字符
        char selected_char = 'A';  // 默认值
        int option_index = 0;
        const char* ptr = options;
        while (*ptr && option_index < selected) {
            if (*ptr == '\n') {
                option_index++;
            }
            ptr++;
        }
        if (*ptr && *ptr != '\n') {
            selected_char = *ptr;
        }

        // 检查SSID长度限制
        if (g_screen_manager.wifi_manager.custom_ssid_length < sizeof(g_screen_manager.wifi_manager.custom_ssid) - 1) {
            // 添加字符到自定义SSID
            g_screen_manager.wifi_manager.custom_ssid[g_screen_manager.wifi_manager.custom_ssid_length] = selected_char;
            g_screen_manager.wifi_manager.custom_ssid_length++;
            g_screen_manager.wifi_manager.custom_ssid[g_screen_manager.wifi_manager.custom_ssid_length] = '\0';

            // 更新SSID显示
            update_custom_ssid_display(current_screen);

            ESP_LOGI(TAG, "Added character '%c' to custom SSID, length: %d",
                     selected_char, g_screen_manager.wifi_manager.custom_ssid_length);
        } else {
            ESP_LOGW(TAG, "Custom SSID length limit reached");
        }
    }
}

/*---- WiFi密码输入屏幕事件处理器实现 ----*/

void screen_wifi_password_roller_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t* roller = lv_event_get_target(e);
    lv_obj_t* screen = (lv_obj_t*)lv_event_get_user_data(e);

    if (code == LV_EVENT_VALUE_CHANGED && screen && roller) {
        // 记录roller变化（用于调试）
        uint16_t selected = lv_roller_get_selected(roller);
        ESP_LOGD(TAG, "WiFi password roller changed to: %u", (unsigned int)selected);

        // 更新密码显示
        update_password_display(screen);
    }
}

// 新的字符添加事件处理函数
void screen_wifi_password_char_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    int roller_type = (int)(intptr_t)lv_event_get_user_data(e);  // 0=小写, 1=大写, 2=数字

    if (code == LV_EVENT_CLICKED) {
        lv_obj_t* current_screen = lv_scr_act();
        if (!current_screen) return;

        // 找到主容器
        lv_obj_t* main_container = NULL;
        for (uint32_t i = 0; i < lv_obj_get_child_cnt(current_screen); i++) {
            lv_obj_t* child = lv_obj_get_child(current_screen, i);
            if (lv_obj_get_child_cnt(child) >= 9) {  // 包含3个roller + 3个按钮 + 3个标签的容器
                main_container = child;
                break;
            }
        }

        if (!main_container) return;

        // 找到对应的roller
        lv_obj_t* roller = NULL;
        int roller_count = 0;
        for (uint32_t i = 0; i < lv_obj_get_child_cnt(main_container); i++) {
            lv_obj_t* child = lv_obj_get_child(main_container, i);
            // 检查是否是roller对象
            if (lv_obj_check_type(child, &lv_roller_class)) {
                if (roller_count == roller_type) {
                    roller = child;
                    break;
                }
                roller_count++;
            }
        }

        if (!roller) return;

        // 获取选中的字符
        uint16_t selected = lv_roller_get_selected(roller);
        char selected_char = 0;

        switch (roller_type) {
            case 0: // 小写字母 a-z
                if (selected < 26) {
                    selected_char = 'a' + selected;
                }
                break;
            case 1: // 大写字母 A-Z
                if (selected < 26) {
                    selected_char = 'A' + selected;
                }
                break;
            case 2: // 数字 0-9
                if (selected < 10) {
                    selected_char = '0' + selected;
                }
                break;
        }

        if (selected_char && g_screen_manager.wifi_manager.input_password_length < 64) {
            // 添加字符到密码缓冲区
            g_screen_manager.wifi_manager.input_password[g_screen_manager.wifi_manager.input_password_length] = selected_char;
            g_screen_manager.wifi_manager.input_password_length++;
            g_screen_manager.wifi_manager.input_password[g_screen_manager.wifi_manager.input_password_length] = '\0';

            // 更新密码显示
            update_wifi_password_display(current_screen);

            ESP_LOGI(TAG, "Added character '%c' to password, length: %d", selected_char, g_screen_manager.wifi_manager.input_password_length);
        }
    }
}

// 退格（删除最后一个字符）事件处理函数
void screen_wifi_password_clear_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_CLICKED) {
        // 检查是否有字符可以删除
        if (g_screen_manager.wifi_manager.input_password_length > 0) {
            // 删除最后一个字符（退格功能）
            g_screen_manager.wifi_manager.input_password_length--;
            g_screen_manager.wifi_manager.input_password[g_screen_manager.wifi_manager.input_password_length] = '\0';

            ESP_LOGI(TAG, "Deleted last character, password length now: %d",
                     g_screen_manager.wifi_manager.input_password_length);
        } else {
            ESP_LOGD(TAG, "No characters to delete");
        }

        // 更新密码显示
        lv_obj_t* current_screen = lv_scr_act();
        if (current_screen) {
            update_wifi_password_display(current_screen);
        }
    }
}

// 更新WiFi密码显示函数（新设计）
void update_wifi_password_display(lv_obj_t* screen)
{
    if (!screen) return;

    lv_obj_t* password_display = (lv_obj_t*)lv_obj_get_user_data(screen);
    if (!password_display) return;

    // 创建显示字符串，直接显示明文密码
    char display_text[80];
    int password_length = g_screen_manager.wifi_manager.input_password_length;

    // 直接显示实际密码（明文）
    snprintf(display_text, sizeof(display_text), "Password: %s",
             g_screen_manager.wifi_manager.input_password);
    lv_label_set_text(password_display, display_text);

    // 密码长度验证：查找并控制Connect按钮状态
    lv_obj_t* connect_btn = find_connect_button(screen);
    if (connect_btn) {
        if (password_length < 8) {
            // 密码长度不足8位，禁用Connect按钮
            lv_obj_add_state(connect_btn, LV_STATE_DISABLED);
            lv_obj_set_style_bg_color(connect_btn, lv_color_hex(0x666666), LV_STATE_DISABLED);  // 灰色
            ESP_LOGD(TAG, "Connect button disabled - password length: %d (minimum: 8)", password_length);
        } else {
            // 密码长度足够，启用Connect按钮
            lv_obj_clear_state(connect_btn, LV_STATE_DISABLED);
            lv_obj_set_style_bg_color(connect_btn, lv_color_hex(0x00AA00), 0);  // 绿色
            ESP_LOGD(TAG, "Connect button enabled - password length: %d", password_length);
        }
    }

    // 保存实际密码到连接用的缓冲区
    strncpy(g_screen_manager.wifi_manager.saved_password,
            g_screen_manager.wifi_manager.input_password,
            sizeof(g_screen_manager.wifi_manager.saved_password) - 1);
    g_screen_manager.wifi_manager.saved_password[sizeof(g_screen_manager.wifi_manager.saved_password) - 1] = '\0';
}

// 旧的密码显示函数（保留兼容性）
void update_password_display(lv_obj_t* screen)
{
    // 对于新设计，直接调用新的函数
    update_wifi_password_display(screen);
}

void screen_wifi_password_control_handler(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    int action = (int)(intptr_t)lv_event_get_user_data(e);

    if (code == LV_EVENT_CLICKED) {
        lv_obj_t* current_screen = lv_scr_act();
        if (!current_screen) {
            ESP_LOGE(TAG, "Current screen is NULL");
            return;
        }

        if (action == 0) {
            // 取消按钮
            ESP_LOGI(TAG, "WiFi password input cancelled");

            // 清理密码输入缓冲区
            memset(g_screen_manager.wifi_manager.input_password, 0, sizeof(g_screen_manager.wifi_manager.input_password));
            g_screen_manager.wifi_manager.input_password_length = 0;

            // 清理自定义网络模式状态
            if (g_screen_manager.wifi_manager.is_custom_network_mode) {
                memset(g_screen_manager.wifi_manager.custom_ssid, 0, sizeof(g_screen_manager.wifi_manager.custom_ssid));
                g_screen_manager.wifi_manager.custom_ssid_length = 0;
                g_screen_manager.wifi_manager.is_custom_network_mode = false;
                ESP_LOGI(TAG, "Cleared custom network mode state");
            }

            // 安全地切换到WiFi配置屏幕
            screen_manager_switch_to(SCREEN_WIFI_CONFIG);

        } else if (action == 1) {
            // 连接按钮
            ESP_LOGI(TAG, "WiFi password connect button pressed");

            // 确保密码显示是最新的
            update_wifi_password_display(current_screen);

            // 使用新的密码输入缓冲区
            const char* password = g_screen_manager.wifi_manager.input_password;
            int password_length = g_screen_manager.wifi_manager.input_password_length;

            if (!password || password_length == 0) {
                ESP_LOGW(TAG, "Password is empty");
                // 查找并更新状态标签
                lv_obj_t* status_label = find_status_label(current_screen);
                if (status_label) {
                    lv_label_set_text(status_label, "Password cannot be empty");
                    lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
                }
                return;
            }

            // 检查密码长度是否足够（最少8个字符）
            if (password_length < 8) {
                ESP_LOGW(TAG, "Password too short: %d characters (minimum: 8)", password_length);
                // 查找并更新状态标签
                lv_obj_t* status_label = find_status_label(current_screen);
                if (status_label) {
                    lv_label_set_text(status_label, "Password must be at least 8 characters");
                    lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
                }
                return;
            }

            // 获取SSID（根据模式选择）
            const char* ssid;
            if (g_screen_manager.wifi_manager.is_custom_network_mode) {
                // 自定义网络模式：使用用户输入的SSID
                ssid = g_screen_manager.wifi_manager.custom_ssid;
                if (!ssid || strlen(ssid) == 0) {
                    ESP_LOGW(TAG, "Custom SSID is empty");
                    lv_obj_t* status_label = find_status_label(current_screen);
                    if (status_label) {
                        lv_label_set_text(status_label, "Network name cannot be empty");
                        lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
                    }
                    return;
                }
                ESP_LOGI(TAG, "Using custom SSID: %s", ssid);
            } else {
                // 普通模式：使用预选的SSID
                ssid = g_screen_manager.wifi_manager.saved_ssid;
                if (!ssid || strlen(ssid) == 0) {
                    ESP_LOGE(TAG, "SSID not found");
                    lv_obj_t* status_label = find_status_label(current_screen);
                    if (status_label) {
                        lv_label_set_text(status_label, "SSID not found");
                        lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
                    }
                    return;
                }
            }

            ESP_LOGI(TAG, "Attempting to connect to WiFi: %s with password length: %d", ssid, strlen(password));

            // 安全地查找并更新状态标签
            lv_obj_t* status_label = find_status_label(current_screen);
            if (status_label) {
                lv_label_set_text(status_label, "Connecting to WiFi...");
                lv_obj_set_style_text_color(status_label, lv_color_hex(0xFFFF00), 0);  // 黄色
            }

            // 安全地查找并禁用连接按钮
            lv_obj_t* connect_btn = find_connect_button(current_screen);
            if (connect_btn) {
                lv_obj_add_state(connect_btn, LV_STATE_DISABLED);
                lv_obj_set_style_bg_color(connect_btn, lv_color_hex(0x666666), 0);  // 灰色
            }

            // 尝试连接WiFi
            esp_err_t ret = wifi_connect(ssid, password);
            if (ret == ESP_OK) {
                ESP_LOGI(TAG, "WiFi connection initiated");

                // 保存WiFi凭据到缓冲区（但不保存到NVS，等连接成功后再保存）
                // 确保SSID也保存到saved_ssid中（对于自定义网络很重要）
                strncpy(g_screen_manager.wifi_manager.saved_ssid, ssid, sizeof(g_screen_manager.wifi_manager.saved_ssid) - 1);
                g_screen_manager.wifi_manager.saved_ssid[sizeof(g_screen_manager.wifi_manager.saved_ssid) - 1] = '\0';

                strncpy(g_screen_manager.wifi_manager.saved_password, password, sizeof(g_screen_manager.wifi_manager.saved_password) - 1);
                g_screen_manager.wifi_manager.saved_password[sizeof(g_screen_manager.wifi_manager.saved_password) - 1] = '\0';

                // 显示正在连接状态
                if (status_label) {
                    lv_label_set_text(status_label, "Connecting to WiFi...");
                    lv_obj_set_style_text_color(status_label, lv_color_hex(0xFFAA00), 0);  // 橙色
                }

                // 启动连接超时定时器（30秒超时）
                if (g_screen_manager.wifi_connection_timeout_timer) {
                    lv_timer_del(g_screen_manager.wifi_connection_timeout_timer);
                }
                g_screen_manager.wifi_connection_timeout_timer = lv_timer_create(wifi_connection_timeout_timer_cb, 30000, NULL);
                if (g_screen_manager.wifi_connection_timeout_timer) {
                    lv_timer_set_repeat_count(g_screen_manager.wifi_connection_timeout_timer, 1);
                    ESP_LOGI(TAG, "Started WiFi connection timeout timer (30s)");
                }

                // 连接结果将通过WiFi事件处理函数确认

            } else {
                ESP_LOGE(TAG, "Failed to initiate WiFi connection: %s", esp_err_to_name(ret));

                // 更新状态
                if (status_label) {
                    lv_label_set_text(status_label, "Failed to start connection");
                    lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
                }

                // 重新启用连接按钮
                if (connect_btn) {
                    lv_obj_clear_state(connect_btn, LV_STATE_DISABLED);
                    lv_obj_set_style_bg_color(connect_btn, lv_color_hex(0x00AA00), 0);  // 恢复绿色
                }
            }
        }
    }
}

// WiFi连接结果处理函数（需要在WiFi事件回调中调用）
void handle_wifi_connection_result(bool success, const char* error_msg)
{
    if (g_screen_manager.current_screen != SCREEN_WIFI_PASSWORD) {
        return;  // 不在WiFi密码输入屏幕，不处理
    }

    // 清理连接超时定时器
    if (g_screen_manager.wifi_connection_timeout_timer) {
        lv_timer_del(g_screen_manager.wifi_connection_timeout_timer);
        g_screen_manager.wifi_connection_timeout_timer = NULL;
        ESP_LOGI(TAG, "Cleared WiFi connection timeout timer");
    }

    lv_obj_t* current_screen = lv_scr_act();
    if (!current_screen) {
        ESP_LOGE(TAG, "Current screen is NULL in connection result handler");
        return;
    }

    // 使用安全的对象查找方法
    lv_obj_t* status_label = find_status_label(current_screen);
    lv_obj_t* connect_btn = find_connect_button(current_screen);

    if (success) {
        ESP_LOGI(TAG, "WiFi connection successful");

        if (status_label) {
            lv_label_set_text(status_label, "Connected! Returning...");
            lv_obj_set_style_text_color(status_label, lv_color_hex(0x00FF00), 0);  // 绿色
        }

        // 连接成功后保存WiFi凭据到NVS
        const char* ssid = g_screen_manager.wifi_manager.saved_ssid;
        const char* password = g_screen_manager.wifi_manager.saved_password;
        if (ssid && password) {
            esp_err_t save_ret = save_wifi_credentials(ssid, password);
            if (save_ret == ESP_OK) {
                ESP_LOGI(TAG, "WiFi credentials saved successfully");
            } else {
                ESP_LOGW(TAG, "Failed to save WiFi credentials: %s", esp_err_to_name(save_ret));
            }
        }

        // 不在这里删除WiFi密码屏幕对象，留给定时器回调处理
        // 这样可以避免立即删除导致的对象引用问题

        // 使用定时器延迟返回初始屏幕
        lv_timer_t* delay_timer = lv_timer_create(wifi_connection_success_timer_cb, 2000, NULL);
        if (delay_timer) {
            lv_timer_set_repeat_count(delay_timer, 1);
            ESP_LOGI(TAG, "Created delay timer for screen transition");
        } else {
            ESP_LOGE(TAG, "Failed to create delay timer");
        }

    } else {
        ESP_LOGW(TAG, "WiFi connection failed: %s", error_msg ? error_msg : "Unknown error");

        if (status_label) {
            lv_label_set_text_fmt(status_label, "Connection failed: %s", error_msg ? error_msg : "Unknown error");
            lv_obj_set_style_text_color(status_label, lv_color_hex(0xFF0000), 0);  // 红色
        }

        // 重新启用连接按钮
        if (connect_btn) {
            lv_obj_clear_state(connect_btn, LV_STATE_DISABLED);
            lv_obj_set_style_bg_color(connect_btn, lv_color_hex(0x00AA00), 0);  // 恢复绿色
        }
    }
}

/*---- WiFi事件处理器实现 ----*/

static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT) {
        switch (event_id) {
            case WIFI_EVENT_STA_START:
                ESP_LOGI(TAG, "WiFi station started");
                g_screen_manager.wifi_manager.status = WIFI_STATUS_DISCONNECTED;
                break;

            case WIFI_EVENT_STA_CONNECTED:
                ESP_LOGI(TAG, "WiFi station connected to AP");
                g_screen_manager.wifi_manager.status = WIFI_STATUS_CONNECTING;  // 仍在连接中，等待IP

                // 获取连接信息
                wifi_ap_record_t ap_info;
                if (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK) {
                    strncpy(g_screen_manager.wifi_manager.connected_ssid,
                           (char*)ap_info.ssid,
                           sizeof(g_screen_manager.wifi_manager.connected_ssid) - 1);
                    g_screen_manager.wifi_manager.signal_strength = ap_info.rssi;
                }

                // 更新WiFi状态指示器为连接中
                if (g_screen_manager.wifi_status_indicator) {
                    update_wifi_status_indicator(g_screen_manager.wifi_status_indicator,
                                                WIFI_STATUS_CONNECTING,
                                                g_screen_manager.wifi_manager.signal_strength);
                }

                // 不在这里处理连接成功，等待IP_EVENT_STA_GOT_IP事件
                break;

            case WIFI_EVENT_STA_DISCONNECTED:
                ESP_LOGI(TAG, "WiFi station disconnected");
                g_screen_manager.wifi_manager.status = WIFI_STATUS_DISCONNECTED;
                memset(g_screen_manager.wifi_manager.connected_ssid, 0,
                       sizeof(g_screen_manager.wifi_manager.connected_ssid));
                g_screen_manager.wifi_manager.signal_strength = 0;

                // 更新WiFi状态指示器
                if (g_screen_manager.wifi_status_indicator) {
                    update_wifi_status_indicator(g_screen_manager.wifi_status_indicator,
                                                WIFI_STATUS_DISCONNECTED, 0);
                }

                // 处理断开连接事件
                wifi_event_sta_disconnected_t* disconnected = (wifi_event_sta_disconnected_t*)event_data;
                if (disconnected) {
                    const char* reason_str = "Connection failed";
                    bool should_auto_reconnect = false;

                    switch (disconnected->reason) {
                        case WIFI_REASON_AUTH_FAIL:
                            reason_str = "Wrong password";
                            // 认证失败不自动重连，需要用户重新输入密码
                            break;
                        case WIFI_REASON_NO_AP_FOUND:
                            reason_str = "Network not found";
                            should_auto_reconnect = true;  // 网络不存在，可能是暂时的，可以重连
                            break;
                        case WIFI_REASON_ASSOC_FAIL:
                            reason_str = "Association failed";
                            should_auto_reconnect = true;  // 关联失败，可以重连
                            break;
                        case WIFI_REASON_BEACON_TIMEOUT:
                        case WIFI_REASON_HANDSHAKE_TIMEOUT:
                        case WIFI_REASON_CONNECTION_FAIL:
                            reason_str = "Connection lost";
                            should_auto_reconnect = true;  // 连接丢失，应该自动重连
                            break;
                        default:
                            reason_str = "Connection failed";
                            should_auto_reconnect = true;  // 其他原因，尝试重连
                            break;
                    }

                    // 如果在WiFi密码输入屏幕，处理连接失败
                    if (g_screen_manager.current_screen == SCREEN_WIFI_PASSWORD) {
                        handle_wifi_connection_result(false, reason_str);
                    }

                    // 启动自动重连（如果适用且有保存的凭据）
                    if (should_auto_reconnect &&
                        g_screen_manager.wifi_manager.auto_connect_enabled &&
                        strlen(g_screen_manager.wifi_manager.saved_ssid) > 0) {
                        ESP_LOGI(TAG, "Starting auto-reconnect due to: %s", reason_str);
                        start_wifi_auto_reconnect(5000);  // 5秒后开始重连
                    }
                }
                break;

            case WIFI_EVENT_SCAN_DONE:
                ESP_LOGI(TAG, "WiFi scan completed");
                g_screen_manager.wifi_manager.status = WIFI_STATUS_DISCONNECTED;  // 扫描完成后恢复状态

                // 更新WiFi配置屏幕的列表显示
                update_wifi_list_display();
                break;

            default:
                break;
        }
    } else if (event_base == IP_EVENT) {
        switch (event_id) {
            case IP_EVENT_STA_GOT_IP:
                ESP_LOGI(TAG, "WiFi got IP address - connection fully established");
                ip_event_got_ip_t* event = (ip_event_got_ip_t*)event_data;
                ESP_LOGI(TAG, "Got IP: " IPSTR, IP2STR(&event->ip_info.ip));

                // 现在才认为WiFi真正连接成功
                g_screen_manager.wifi_manager.status = WIFI_STATUS_CONNECTED;

                // 停止自动重连定时器（连接成功）
                stop_wifi_auto_reconnect();

                // 更新WiFi状态指示器为已连接
                if (g_screen_manager.wifi_status_indicator) {
                    update_wifi_status_indicator(g_screen_manager.wifi_status_indicator,
                                                WIFI_STATUS_CONNECTED,
                                                g_screen_manager.wifi_manager.signal_strength);
                }

                // 如果在WiFi密码输入屏幕，处理连接成功
                handle_wifi_connection_result(true, NULL);
                break;

            default:
                break;
        }
    }
}

/*---- WiFi状态定时器实现 ----*/

void wifi_status_timer_callback(lv_timer_t* timer)
{
    // 定期更新WiFi状态指示器
    if (g_screen_manager.wifi_status_indicator && g_screen_manager.current_screen == SCREEN_INITIAL) {
        update_wifi_status_indicator(g_screen_manager.wifi_status_indicator,
                                    wifi_get_status(),
                                    wifi_get_signal_strength());
    }

    // 检查是否需要启动自动重连
    if (g_screen_manager.wifi_manager.status == WIFI_STATUS_DISCONNECTED &&
        g_screen_manager.wifi_manager.auto_connect_enabled &&
        strlen(g_screen_manager.wifi_manager.saved_ssid) > 0 &&
        !g_screen_manager.wifi_reconnect_timer) {

        ESP_LOGI(TAG, "WiFi disconnected with saved credentials, starting auto-reconnect");
        start_wifi_auto_reconnect(10000);  // 10秒后开始重连
    }
}

/*---- 自动连接功能实现 ----*/

esp_err_t wifi_auto_connect_on_boot(void)
{
    // 默认启用自动连接
    g_screen_manager.wifi_manager.auto_connect_enabled = true;

    if (!g_screen_manager.wifi_manager.auto_connect_enabled) {
        ESP_LOGI(TAG, "Auto-connect disabled");
        return ESP_OK;
    }

    char saved_ssid[33] = {0};
    char saved_password[65] = {0};

    esp_err_t ret = load_wifi_credentials(saved_ssid, saved_password);
    if (ret != ESP_OK) {
        ESP_LOGI(TAG, "No saved WiFi credentials found");
        return ESP_OK;  // 没有保存的凭据，不是错误
    }

    ESP_LOGI(TAG, "Attempting auto-connect to saved WiFi: %s", saved_ssid);

    // 保存到管理器
    strncpy(g_screen_manager.wifi_manager.saved_ssid, saved_ssid,
           sizeof(g_screen_manager.wifi_manager.saved_ssid) - 1);
    strncpy(g_screen_manager.wifi_manager.saved_password, saved_password,
           sizeof(g_screen_manager.wifi_manager.saved_password) - 1);

    // 尝试连接
    return wifi_connect(saved_ssid, saved_password);
}

// 启动WiFi自动重连机制
void start_wifi_auto_reconnect(uint32_t delay_ms)
{
    // 检查是否有保存的WiFi凭据
    if (strlen(g_screen_manager.wifi_manager.saved_ssid) == 0) {
        ESP_LOGW(TAG, "No saved WiFi credentials, cannot start auto-reconnect");
        return;
    }

    // 停止现有的重连定时器
    stop_wifi_auto_reconnect();

    // 重置重连尝试次数
    g_screen_manager.reconnect_attempt_count = 0;

    // 创建自动重连定时器（重复执行）
    g_screen_manager.wifi_reconnect_timer = lv_timer_create(wifi_auto_reconnect_timer_cb, delay_ms, NULL);
    if (g_screen_manager.wifi_reconnect_timer) {
        // 设置为重复执行，每次间隔逐渐增加
        lv_timer_set_repeat_count(g_screen_manager.wifi_reconnect_timer, -1);  // 无限重复
        ESP_LOGI(TAG, "Started WiFi auto-reconnect timer with %lu ms delay", (unsigned long)delay_ms);
    } else {
        ESP_LOGE(TAG, "Failed to create WiFi auto-reconnect timer");
    }
}

// 停止WiFi自动重连机制
void stop_wifi_auto_reconnect(void)
{
    if (g_screen_manager.wifi_reconnect_timer) {
        lv_timer_del(g_screen_manager.wifi_reconnect_timer);
        g_screen_manager.wifi_reconnect_timer = NULL;
        ESP_LOGI(TAG, "Stopped WiFi auto-reconnect timer");
    }
    g_screen_manager.reconnect_attempt_count = 0;
}
